{"version": 3, "file": "betting.d.ts", "sourceRoot": "", "sources": ["../../src/models/betting.ts"], "names": [], "mappings": "AAAA,MAAM,MAAM,SAAS,GAAG,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC;AAClD,MAAM,MAAM,OAAO,GACf,cAAc,GACd,cAAc,GACd,YAAY,GACZ,eAAe,GACf,YAAY,GACZ,oBAAoB,GACpB,mBAAmB,GACnB,eAAe,GACf,UAAU,CAAC;AAEf,MAAM,WAAW,kBAAkB;IACjC,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE;QACL,EAAE,EAAE,MAAM,CAAC;QACX,KAAK,EAAE,MAAM,CAAC;QACd,KAAK,EAAE,MAAM,CAAC;QACd,IAAI,EAAE,IAAI,CAAC;QACX,UAAU,EAAE,MAAM,CAAC;QACnB,KAAK,EAAE,MAAM,CAAC;KACf,CAAC;IAEF,GAAG,EAAE;QACH,IAAI,EAAE,OAAO,CAAC;QACd,SAAS,EAAE,MAAM,CAAC;QAClB,IAAI,EAAE,MAAM,CAAC;QACb,UAAU,EAAE,MAAM,CAAC;QACnB,KAAK,CAAC,EAAE,MAAM,CAAC;KAChB,CAAC;IAEF,IAAI,EAAE;QACJ,KAAK,EAAE,SAAS,CAAC;QACjB,OAAO,EAAE,MAAM,EAAE,CAAC;QAClB,iBAAiB,EAAE,MAAM,EAAE,CAAC;KAC7B,CAAC;IAEF,QAAQ,EAAE;QACR,UAAU,EAAE,MAAM,CAAC;QACnB,aAAa,EAAE,MAAM,CAAC;QACtB,kBAAkB,EAAE,MAAM,CAAC;QAC3B,mBAAmB,EAAE,MAAM,CAAC;QAE5B,OAAO,EAAE;YACP,YAAY,EAAE,MAAM,CAAC;YACrB,UAAU,EAAE,MAAM,CAAC;YACnB,gBAAgB,EAAE,MAAM,EAAE,CAAC;YAC3B,iBAAiB,EAAE,MAAM,EAAE,CAAC;YAC5B,eAAe,EAAE,MAAM,EAAE,CAAC;YAC1B,UAAU,CAAC,EAAE,MAAM,CAAC;YACpB,iBAAiB,EAAE,MAAM,EAAE,CAAC;SAC7B,CAAC;QAEF,aAAa,EAAE,MAAM,CAAC;QACtB,QAAQ,EAAE,MAAM,EAAE,CAAC;QAEnB,YAAY,CAAC,EAAE;YACb,OAAO,EAAE,MAAM,CAAC;YAChB,OAAO,EAAE,MAAM,CAAC;YAChB,SAAS,EAAE,IAAI,GAAG,MAAM,GAAG,QAAQ,CAAC;YACpC,YAAY,EAAE,OAAO,GAAG,UAAU,GAAG,OAAO,CAAC;SAC9C,CAAC;KACH,CAAC;IAEF,WAAW,EAAE;QACX,YAAY,EAAE,MAAM,CAAC;QACrB,WAAW,EAAE,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;QACvC,WAAW,EAAE,IAAI,CAAC;QAClB,OAAO,EAAE,MAAM,EAAE,CAAC;KACnB,CAAC;CACH;AAED,MAAM,WAAW,YAAY;IAC3B,GAAG,EAAE;QACH,UAAU,EAAE,MAAM,CAAC;QACnB,kBAAkB,EAAE,MAAM,CAAC;QAC3B,eAAe,EAAE,MAAM,CAAC;QACxB,iBAAiB,EAAE,MAAM,EAAE,CAAC;QAC5B,gBAAgB,EAAE,MAAM,CAAC;QACzB,UAAU,EAAE,MAAM,CAAC;QACnB,aAAa,EAAE,MAAM,CAAC;QACtB,sBAAsB,EAAE,MAAM,EAAE,CAAC;KAClC,CAAC;IAEF,MAAM,EAAE;QACN,UAAU,EAAE,MAAM,CAAC;QACnB,kBAAkB,EAAE,MAAM,CAAC;QAC3B,eAAe,EAAE,MAAM,CAAC;QACxB,gBAAgB,EAAE,MAAM,EAAE,CAAC;QAC3B,gBAAgB,EAAE,MAAM,CAAC;QACzB,UAAU,EAAE,MAAM,CAAC;QACnB,aAAa,EAAE,MAAM,CAAC;KACvB,CAAC;IAEF,SAAS,EAAE;QACT,UAAU,EAAE,MAAM,CAAC;QACnB,qBAAqB,EAAE,MAAM,CAAC;QAC9B,eAAe,EAAE,MAAM,CAAC;QACxB,kBAAkB,EAAE,MAAM,EAAE,CAAC;KAC9B,CAAC;CACH;AAED,MAAM,WAAW,cAAc;IAC7B,QAAQ,EAAE;QACR,YAAY,EAAE,IAAI,CAAC;QACnB,UAAU,EAAE,MAAM,CAAC;QACnB,YAAY,EAAE,MAAM,CAAC;QACrB,iBAAiB,EAAE,MAAM,CAAC;QAC1B,aAAa,EAAE,MAAM,CAAC;QACtB,eAAe,EAAE,MAAM,CAAC;KACzB,CAAC;IAEF,aAAa,EAAE,kBAAkB,EAAE,CAAC;IAEpC,OAAO,EAAE;QACP,kBAAkB,EAAE,kBAAkB,EAAE,CAAC;QACzC,gBAAgB,EAAE;YAChB,GAAG,EAAE,MAAM,CAAC;YACZ,MAAM,EAAE,MAAM,CAAC;YACf,IAAI,EAAE,MAAM,CAAC;SACd,CAAC;QACF,kBAAkB,EAAE,kBAAkB,EAAE,CAAC;QACzC,oBAAoB,EAAE,MAAM,EAAE,CAAC;KAChC,CAAC;IAEF,WAAW,EAAE;QACX,mBAAmB,EAAE,MAAM,CAAC;QAC5B,gBAAgB,EAAE,MAAM,CAAC;QACzB,iBAAiB,EAAE,MAAM,EAAE,CAAC;QAC5B,eAAe,EAAE,MAAM,EAAE,CAAC;KAC3B,CAAC;IAEF,QAAQ,EAAE,MAAM,EAAE,CAAC;IACnB,WAAW,EAAE,MAAM,EAAE,CAAC;CACvB"}