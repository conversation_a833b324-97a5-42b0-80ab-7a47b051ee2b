# Ultimate CS2 Betting Analytics System Development

## Project Overview
Develop a production-ready CS2 betting analytics system that leverages the HLTV API to identify high-value betting opportunities with maximum accuracy. The system must use exclusively real data from live API endpoints and follow the comprehensive betting framework outlined below.

## Technical Setup Requirements

### 1. HLTV API Integration
- **Primary Data Source**: Use https://github.com/eupeutro/hltv-api exclusively
- **Docker Setup**: Configure the API locally using Docker for consistent environment
- **No Mock Data**: All analysis must use real, live data from API endpoints
- **Rate Limiting**: Implement proper rate limiting to respect API constraints

### 2. Development Environment
- **Platform**: Windows 11 compatibility required
- **Language**: TypeScript with Node.js
- **Architecture**: Modular design with clear separation of concerns
- **Documentation**: Comprehensive README with setup instructions

### 3. Date Flexibility
Implement three analysis modes:
- **Today**: Analyze matches for current date
- **Tomorrow**: Analyze matches for next day
- **Custom Date**: User-specified date analysis

## Core Analysis Framework

### Primary Objective
Identify and rank the TOP 5 highest-value CS2 betting opportunities using verified data, prioritizing positive Expected Value (EV) bets where assessed probability exceeds implied odds probability.

### Data Collection Requirements

#### Primary Data Sources (Required)
1. **HLTV Match Listings**: Upcoming matches with complete details
2. **HLTV Team Rankings**: Current world rankings and rating history
3. **HLTV Team Statistics**: Last 3 months performance data
4. **HLTV Player Statistics**: Individual player metrics (last 3 months)
5. **HLTV Match History**: Head-to-head records and recent form
6. **HLTV Betting Odds**: Current odds and line movements

#### Key Metrics to Extract
- Team rankings and rating changes
- Win/loss records (overall and map-specific)
- Player individual ratings and performance trends
- Map pool statistics and preferences
- Recent form (last 5-10 matches)
- Head-to-head historical data

### Risk Assessment Tiers

#### 🛡️ Low Risk Criteria (ALL must be met)
- HLTV ranking gap ≥ 10 places between teams
- Favored team win rate ≥ 70% in last 10 matches
- Stable roster (no changes in last 3 months)
- BO3/BO5 format only
- Clear map pool advantage (≥ 65% win rate on ≥2 maps)
- Team average rating ≥ 1.10 across last 3 months
- Pistol round win rate ≥ 55%
- No reported team issues or conflicts

#### ⚖️ Medium Risk Criteria (ALL must be met)
- HLTV ranking gap ≥ 5 places between teams
- Favored team win rate ≥ 60% in last 10 matches
- Stable roster (no major changes in last month)
- BO3/BO5 preferred (BO1 with exceptional justification)
- Map pool advantage (≥ 60% win rate on ≥1 map)
- Team average rating ≥ 1.05 across last 3 months
- Pistol round win rate ≥ 50%

#### 🚫 High Risk (Automatically Excluded)
- Teams with <55% recent win rate
- Major roster changes in past 30 days
- Insufficient verifiable data
- Key players with <0.95 rating in last 5 matches
- BO1 matches without exceptional value

### Advanced Statistical Analysis

For each qualifying match, calculate and analyze:

#### Team Performance Metrics
- **Opening Kill Success Rate**: ≥55% indicates tempo control
- **Trade Kill Efficiency**: Ratio ≥0.70 shows teamwork
- **Economy Management**: Buy-round win % ≥55%, eco/force win % ≥25%
- **Map Control**: T-side map control percentage
- **Clutch Performance**: Success rate ≥30% in 1vX situations
- **Utility Usage**: Average utility damage ≥20 per round
- **Flash Effectiveness**: Average flash duration ≥1.5 seconds
- **Mid-Round Adaptability**: Win rate after losing pistol ≥40%
- **Post-Plant Success**: Win rate ≥65% in post-plant situations
- **Retake Success**: CT-side retake success ≥40%

#### Player-Specific Analysis
- **Entry Fragger Efficiency**: Entry kill ratio >0.7
- **AWP Impact**: AWPer rating >1.15 and >0.75 kills per round
- **Support Player Value**: High flash assists and utility damage
- **Individual Form**: Recent rating trends and consistency
- **Pressure Performance**: Playoff vs group stage differential
- **Role Compatibility**: Players in natural positions

#### Contextual Factors
- **Recent Patch Adaptation**: Post-update win rate ≥60%
- **Tournament Context**: Historical performance in similar events
- **Travel Impact**: Timezone adjustments within 48 hours
- **Motivation Factors**: Prize pool implications, qualification stakes
- **Stand-in Impact**: If applicable, analyze replacement player fit

### Betting Markets Analysis

Analyze these bet types in priority order:

1. **Match Winner (Moneyline)** - Primary focus
2. **Map Handicap (-1.5/+1.5)** - For clear favorites
3. **Total Maps (Over/Under 2.5)** - Based on match format expectations
4. **Correct Score (2-0, 2-1, etc.)** - For confident predictions
5. **Map Winner** - When specific map advantages identified
6. **Player Performance Markets** - For exceptional individual matchups
7. **First Half Winner** - When CT/T side preferences are clear
8. **Specialty Markets** (Pistol rounds, Overtime) - Only with strong justification

### Value Betting Methodology

#### Expected Value Calculation
1. **Extract odds** from HLTV or betting sources
2. **Calculate implied probability**: (1/decimal odds) × 100
3. **Assess true probability** based on comprehensive analysis
4. **Calculate EV**: (Assessed probability × potential profit) - (Loss probability × stake)
5. **Only recommend positive EV bets**

#### Confidence Rating System
- **Base confidence** (60-80%) from statistical analysis
- **Adjustments**:
  - Data completeness: +/-5%
  - Recent form consistency: +/-5%
  - Intangible factors: +/-5%
- **Maximum confidence**: 90% (acknowledge uncertainty)

### System Architecture Requirements

#### Core Modules
1. **API Client**: HLTV API wrapper with error handling
2. **Data Fetcher**: Collect all required match and team data
3. **Analysis Engine**: Implement betting framework logic
4. **Risk Assessor**: Apply filtering criteria and risk tiers
5. **EV Calculator**: Compute expected values and rank opportunities
6. **Report Generator**: Format output with comprehensive details

#### Data Persistence
- **Caching Strategy**: Store API responses to minimize requests
- **Data Validation**: Ensure data integrity and completeness
- **Update Mechanism**: Refresh data at configurable intervals

#### Error Handling
- **API Failures**: Graceful degradation and retry logic
- **Missing Data**: Clear flagging of insufficient information
- **Rate Limiting**: Respect API constraints with backoff strategies

## Output Format

For each of the TOP 5 betting opportunities:

```
RANK #[X]: [Team A] vs [Team B] — Bet: [Bet Type]
Tournament: [Event Name & Stage]
Date/Time: [Date] at [Time UTC]
Odds: [Decimal] (Source: HLTV/API)
Risk Level: [🛡️ Low / ⚖️ Medium]
Confidence: [60-90%]
Expected Value: [+X.X%]

ANALYSIS SUMMARY:
• Team Rankings: [Team A: #X] vs [Team B: #Y] (Gap: X places)
• Recent Form: [Team A: X-Y] vs [Team B: X-Y] (Last 10 matches)
• Map Pool Advantage: [Specific maps where Team A has edge]
• Key Player Matchups: [Star player comparisons]
• Advanced Metrics: [Opening kills, economy, clutch rates]
• Head-to-Head: [Historical record if available]
• Tournament Context: [Stakes, format, travel factors]

JUSTIFICATION:
[Detailed explanation of why this bet offers value]

RED FLAGS:
[Any concerning factors that could impact the bet]

ODDS MOVEMENT:
[Opening vs current odds, significant changes]
```

### Summary Deliverables

1. **Ranked List**: Top 5 bets with complete analysis
2. **Risk Distribution**: Breakdown of low vs medium risk bets
3. **Alternative Options**: Additional bets if primary unavailable
4. **Market Inefficiencies**: Identified value opportunities
5. **Data Quality Report**: Completeness and reliability assessment

## Development Specifications

### Technical Requirements
- **TypeScript**: Strongly typed codebase
- **Docker**: Containerized HLTV API setup
- **Modular Design**: Clear separation of concerns
- **Comprehensive Logging**: Track API calls and analysis steps
- **Configuration**: Environment variables for API settings
- **Testing**: Unit tests for core logic components

### Documentation Requirements
- **Setup Guide**: Complete installation and configuration
- **API Documentation**: Endpoint usage and data structures
- **Analysis Methodology**: Detailed explanation of betting logic
- **Usage Examples**: How to run different analysis modes
- **Troubleshooting**: Common issues and solutions

### Quality Assurance
- **Data Validation**: Verify all statistics against HLTV sources
- **Logic Testing**: Ensure risk tiers and EV calculations are correct
- **Performance**: Optimize API calls and processing time
- **Reliability**: Handle edge cases and API inconsistencies

## Execution Modes

1. **Single Analysis**: Run once for specified date
2. **Scheduled Analysis**: Configure for regular updates
3. **Watch Mode**: Continuous monitoring with alerts
4. **Backtest Mode**: Historical analysis for strategy validation

## Success Criteria

- **Accuracy**: All data verifiable against HLTV sources
- **Value Focus**: Only positive EV recommendations
- **Risk Management**: Clear tier classification
- **Usability**: Simple execution with comprehensive output
- **Reliability**: Consistent performance across different dates and tournaments

This system should serve as the definitive tool for identifying high-value CS2 betting opportunities using rigorous statistical analysis and real-time data from the HLTV ecosystem.