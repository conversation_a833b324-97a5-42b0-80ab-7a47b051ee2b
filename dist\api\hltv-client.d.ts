export interface HLTVRequestOptions {
    retries?: number;
    delay?: number;
}
export declare class HLTVClient {
    private requestCount;
    private lastRequestTime;
    constructor();
    /**
     * Rate limiting wrapper for HLTV requests
     */
    private rateLimit;
    /**
     * Execute HLTV request with retry logic
     */
    private executeRequest;
    /**
     * Get matches for a specific date
     */
    getMatches(date?: Date): Promise<any[]>;
    /**
     * Get detailed match information
     */
    getMatch(matchId: number): Promise<any>;
    /**
     * Get team ranking
     */
    getTeamRanking(options?: any): Promise<any[]>;
    /**
     * Get team information
     */
    getTeam(teamId: number): Promise<any>;
    /**
     * Get team statistics
     */
    getTeamStats(teamId: number, options?: {
        startDate?: string;
        endDate?: string;
        matchType?: any;
        rankingFilter?: any;
        maps?: any[];
        bestOfX?: any;
    }): Promise<any>;
    /**
     * Get player information
     */
    getPlayer(playerId: number): Promise<any>;
    /**
     * Get player statistics
     */
    getPlayerStats(playerId: number, options?: {
        startDate?: string;
        endDate?: string;
        matchType?: any;
        rankingFilter?: any;
        maps?: any[];
        bestOfX?: any;
        eventIds?: number[];
    }): Promise<any>;
    /**
     * Get player ranking
     */
    getPlayerRanking(options?: {
        startDate?: string;
        endDate?: string;
        matchType?: any;
        rankingFilter?: any;
        maps?: any[];
        minMapCount?: number;
        countries?: string[];
        bestOfX?: any;
    }): Promise<any[]>;
    /**
     * Get match results
     */
    getResults(options?: any): Promise<any[]>;
    /**
     * Get events
     */
    getEvents(options?: {
        eventType?: any;
        prizePoolMin?: number;
        prizePoolMax?: number;
        attendingTeamIds?: number[];
        attendingPlayerIds?: number[];
    }): Promise<any[]>;
    /**
     * Get event details
     */
    getEvent(eventId: number): Promise<any>;
    /**
     * Get match statistics
     */
    getMatchStats(matchId: number): Promise<any>;
    /**
     * Get request statistics
     */
    getRequestStats(): {
        count: number;
        lastRequestTime: number;
    };
    /**
     * Reset request statistics
     */
    resetStats(): void;
}
//# sourceMappingURL=hltv-client.d.ts.map