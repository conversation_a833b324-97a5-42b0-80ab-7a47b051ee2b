import { RiskCriteria } from '@/models/betting';
export interface AppConfig {
    hltv: {
        requestDelay: number;
        maxConcurrentRequests: number;
        retryAttempts: number;
        retryDelay: number;
    };
    cache: {
        ttl: {
            matches: number;
            teams: number;
            players: number;
            rankings: number;
        };
    };
    analysis: {
        minConfidenceThreshold: number;
        maxConfidenceThreshold: number;
        defaultStakeAmount: number;
        minPositiveEvThreshold: number;
    };
    logging: {
        level: string;
        filePath: string;
    };
    output: {
        format: 'console' | 'json' | 'html';
        saveReports: boolean;
        reportsDir: string;
    };
    risk: RiskCriteria;
    metrics: {
        minOpeningKillRate: number;
        minTradeKillEfficiency: number;
        minEconomyWinRate: number;
        minClutchSuccessRate: number;
        minPistolWinRate: number;
    };
}
export declare const config: AppConfig;
export default config;
//# sourceMappingURL=settings.d.ts.map