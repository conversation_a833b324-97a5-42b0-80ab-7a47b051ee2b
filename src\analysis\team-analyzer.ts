import { TeamStats } from '@/models/team';
import { MathUtils } from '@/utils/math-utils';
import { DateUtils } from '@/utils/date-utils';
import { Logger } from '@/utils/logger';
import { config } from '@/config/settings';

export interface TeamAnalysisResult {
  teamId: number;
  name: string;
  ranking: number;
  overallRating: number;
  
  form: {
    recent: {
      winRate: number;
      matches: number;
      trend: 'improving' | 'declining' | 'stable';
      consistency: number;
    };
    overall: {
      winRate: number;
      totalMatches: number;
    };
  };
  
  performance: {
    openingKills: number;
    tradeKillEfficiency: number;
    economyManagement: number;
    clutchPerformance: number;
    pistolRounds: number;
    mapControl: number;
  };
  
  mapPool: {
    strongMaps: Array<{
      map: string;
      winRate: number;
      confidence: number;
    }>;
    weakMaps: Array<{
      map: string;
      winRate: number;
      confidence: number;
    }>;
    overallAdvantage: number;
  };
  
  roster: {
    stability: number;
    experience: number;
    averageRating: number;
    keyPlayers: Array<{
      name: string;
      role: string;
      rating: number;
      impact: number;
    }>;
  };
  
  contextualFactors: {
    patchAdaptation: number;
    motivationLevel: number;
    travelImpact: number;
    pressureHandling: number;
  };
  
  strengths: string[];
  weaknesses: string[];
  
  dataQuality: {
    completeness: number;
    reliability: 'high' | 'medium' | 'low';
    sampleSize: number;
    lastUpdated: Date;
  };
}

export class TeamAnalyzer {
  /**
   * Analyze team performance and characteristics
   */
  static analyzeTeam(teamData: any, teamStats: any, rankings: any[]): TeamAnalysisResult {
    Logger.debug('Analyzing team', { teamId: teamData.id, name: teamData.name });
    
    const startTime = Date.now();
    
    try {
      const analysis: TeamAnalysisResult = {
        teamId: teamData.id,
        name: teamData.name,
        ranking: this.extractRanking(teamData.id, rankings),
        overallRating: this.calculateOverallRating(teamStats),
        
        form: this.analyzeForm(teamStats),
        performance: this.analyzePerformance(teamStats),
        mapPool: this.analyzeMapPool(teamStats),
        roster: this.analyzeRoster(teamData, teamStats),
        contextualFactors: this.analyzeContextualFactors(teamStats),
        
        strengths: [],
        weaknesses: [],
        
        dataQuality: this.assessDataQuality(teamStats),
      };
      
      // Identify strengths and weaknesses
      analysis.strengths = this.identifyStrengths(analysis);
      analysis.weaknesses = this.identifyWeaknesses(analysis);
      
      const duration = Date.now() - startTime;
      Logger.performance('team_analysis', duration, {
        teamId: analysis.teamId,
        name: analysis.name,
      });
      
      return analysis;
    } catch (error) {
      Logger.error('Team analysis failed', error as Error, {
        teamId: teamData.id,
        name: teamData.name,
      });
      throw error;
    }
  }
  
  /**
   * Extract team ranking from rankings data
   */
  private static extractRanking(teamId: number, rankings: any[]): number {
    const teamRanking = rankings.find(r => r.team?.id === teamId);
    return teamRanking?.place || 999;
  }
  
  /**
   * Calculate overall team rating
   */
  private static calculateOverallRating(teamStats: any): number {
    if (!teamStats) return 0;
    
    // Use multiple factors to calculate overall rating
    const factors = [
      teamStats.rating || 1.0,
      (teamStats.wins / Math.max(teamStats.wins + teamStats.losses, 1)) * 2, // Win rate scaled
      (teamStats.mapsWon / Math.max(teamStats.mapsPlayed, 1)) * 2, // Map win rate scaled
    ];
    
    return MathUtils.weightedAverage(factors, [0.5, 0.3, 0.2]);
  }
  
  /**
   * Analyze team form and recent performance
   */
  private static analyzeForm(teamStats: any): TeamAnalysisResult['form'] {
    const recentMatches = teamStats.recentResults || [];
    const totalMatches = teamStats.wins + teamStats.losses;
    
    // Recent form (last 10 matches)
    const recentWins = recentMatches.filter((m: any) => m.result === 'win').length;
    const recentTotal = recentMatches.length;
    const recentWinRate = recentTotal > 0 ? (recentWins / recentTotal) * 100 : 0;
    
    // Overall form
    const overallWinRate = totalMatches > 0 ? (teamStats.wins / totalMatches) * 100 : 0;
    
    // Trend analysis
    const trend = this.analyzeTrend(recentMatches);
    
    // Consistency (coefficient of variation of recent performance)
    const recentRatings = recentMatches.map((m: any) => m.rating || 1.0);
    const consistency = 100 - MathUtils.coefficientOfVariation(recentRatings);
    
    return {
      recent: {
        winRate: MathUtils.round(recentWinRate, 1),
        matches: recentTotal,
        trend,
        consistency: MathUtils.round(Math.max(0, consistency), 1),
      },
      overall: {
        winRate: MathUtils.round(overallWinRate, 1),
        totalMatches,
      },
    };
  }
  
  /**
   * Analyze performance trends
   */
  private static analyzeTrend(recentMatches: any[]): 'improving' | 'declining' | 'stable' {
    if (recentMatches.length < 5) return 'stable';
    
    const ratings = recentMatches.slice(-5).map((m: any) => m.rating || 1.0);
    const firstHalf = ratings.slice(0, Math.floor(ratings.length / 2));
    const secondHalf = ratings.slice(Math.floor(ratings.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, r) => sum + r, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, r) => sum + r, 0) / secondHalf.length;
    
    const difference = secondAvg - firstAvg;
    
    if (difference > 0.05) return 'improving';
    if (difference < -0.05) return 'declining';
    return 'stable';
  }
  
  /**
   * Analyze team performance metrics
   */
  private static analyzePerformance(teamStats: any): TeamAnalysisResult['performance'] {
    return {
      openingKills: teamStats.openingKillRating || 0,
      tradeKillEfficiency: teamStats.tradeKillRating || 0,
      economyManagement: teamStats.economyRating || 0,
      clutchPerformance: teamStats.clutchRating || 0,
      pistolRounds: teamStats.pistolRoundRating || 0,
      mapControl: teamStats.mapControlRating || 0,
    };
  }
  
  /**
   * Analyze team map pool
   */
  private static analyzeMapPool(teamStats: any): TeamAnalysisResult['mapPool'] {
    const mapStats = teamStats.mapStats || [];
    
    const strongMaps = mapStats
      .filter((m: any) => m.winRate >= 65 && m.played >= 3)
      .map((m: any) => ({
        map: m.name,
        winRate: m.winRate,
        confidence: this.calculateMapConfidence(m.played, m.winRate),
      }))
      .sort((a: any, b: any) => b.winRate - a.winRate);
    
    const weakMaps = mapStats
      .filter((m: any) => m.winRate <= 40 && m.played >= 3)
      .map((m: any) => ({
        map: m.name,
        winRate: m.winRate,
        confidence: this.calculateMapConfidence(m.played, m.winRate),
      }))
      .sort((a: any, b: any) => a.winRate - b.winRate);
    
    const overallAdvantage = this.calculateMapPoolAdvantage(mapStats);
    
    return {
      strongMaps,
      weakMaps,
      overallAdvantage,
    };
  }
  
  /**
   * Calculate map confidence based on sample size and win rate
   */
  private static calculateMapConfidence(played: number, winRate: number): number {
    // Higher confidence with more games played and extreme win rates
    const sampleConfidence = Math.min(played / 10, 1) * 50;
    const winRateConfidence = Math.abs(winRate - 50);
    
    return MathUtils.round(sampleConfidence + winRateConfidence, 1);
  }
  
  /**
   * Calculate overall map pool advantage
   */
  private static calculateMapPoolAdvantage(mapStats: any[]): number {
    if (!mapStats.length) return 0;
    
    const weightedWinRate = MathUtils.weightedAverage(
      mapStats.map(m => m.winRate),
      mapStats.map(m => m.played)
    );
    
    return MathUtils.round(weightedWinRate - 50, 1); // Advantage over 50% baseline
  }
  
  /**
   * Analyze roster characteristics
   */
  private static analyzeRoster(teamData: any, teamStats: any): TeamAnalysisResult['roster'] {
    const players = teamData.players || [];
    
    const stability = this.calculateRosterStability(players);
    const experience = this.calculateAverageExperience(players);
    const averageRating = this.calculateAverageRating(players);
    const keyPlayers = this.identifyKeyPlayers(players);
    
    return {
      stability,
      experience,
      averageRating,
      keyPlayers,
    };
  }
  
  /**
   * Calculate roster stability
   */
  private static calculateRosterStability(players: any[]): number {
    if (!players.length) return 0;
    
    const now = new Date();
    const stableThreshold = 90; // 90 days
    
    const stablePlayers = players.filter(p => {
      if (!p.joinDate) return true; // Assume stable if no join date
      return DateUtils.daysBetween(new Date(p.joinDate), now) >= stableThreshold;
    });
    
    return MathUtils.round((stablePlayers.length / players.length) * 100, 1);
  }
  
  /**
   * Calculate average team experience
   */
  private static calculateAverageExperience(players: any[]): number {
    if (!players.length) return 0;
    
    const experiences = players.map(p => p.experience || 0);
    return MathUtils.round(experiences.reduce((sum, exp) => sum + exp, 0) / experiences.length, 1);
  }
  
  /**
   * Calculate average team rating
   */
  private static calculateAverageRating(players: any[]): number {
    if (!players.length) return 1.0;
    
    const ratings = players.map(p => p.rating || 1.0);
    return MathUtils.round(ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length, 2);
  }
  
  /**
   * Identify key players
   */
  private static identifyKeyPlayers(players: any[]): Array<{
    name: string;
    role: string;
    rating: number;
    impact: number;
  }> {
    return players
      .filter(p => p.rating >= 1.05) // Above average players
      .map(p => ({
        name: p.name,
        role: p.role || 'unknown',
        rating: p.rating || 1.0,
        impact: this.calculatePlayerImpact(p),
      }))
      .sort((a, b) => b.impact - a.impact)
      .slice(0, 3); // Top 3 key players
  }
  
  /**
   * Calculate player impact score
   */
  private static calculatePlayerImpact(player: any): number {
    const factors = [
      player.rating || 1.0,
      player.kpr || 0.7,
      player.adr || 70,
      player.kast || 70,
    ];
    
    const weights = [0.4, 0.2, 0.2, 0.2];
    
    return MathUtils.round(MathUtils.weightedAverage(factors, weights), 2);
  }
  
  /**
   * Analyze contextual factors
   */
  private static analyzeContextualFactors(teamStats: any): TeamAnalysisResult['contextualFactors'] {
    return {
      patchAdaptation: teamStats.patchAdaptationRating || 50,
      motivationLevel: teamStats.motivationRating || 50,
      travelImpact: teamStats.travelImpactRating || 0,
      pressureHandling: teamStats.pressureRating || 50,
    };
  }
  
  /**
   * Assess data quality
   */
  private static assessDataQuality(teamStats: any): TeamAnalysisResult['dataQuality'] {
    const sampleSize = (teamStats.wins || 0) + (teamStats.losses || 0);
    const completeness = this.calculateDataCompleteness(teamStats);
    
    let reliability: 'high' | 'medium' | 'low' = 'low';
    if (sampleSize >= 20 && completeness >= 80) reliability = 'high';
    else if (sampleSize >= 10 && completeness >= 60) reliability = 'medium';
    
    return {
      completeness,
      reliability,
      sampleSize,
      lastUpdated: new Date(),
    };
  }
  
  /**
   * Calculate data completeness percentage
   */
  private static calculateDataCompleteness(teamStats: any): number {
    const requiredFields = [
      'wins', 'losses', 'rating', 'mapStats', 'recentResults',
      'openingKillRating', 'economyRating', 'clutchRating'
    ];
    
    const presentFields = requiredFields.filter(field => 
      teamStats[field] !== undefined && teamStats[field] !== null
    );
    
    return MathUtils.round((presentFields.length / requiredFields.length) * 100, 1);
  }
  
  /**
   * Identify team strengths
   */
  private static identifyStrengths(analysis: TeamAnalysisResult): string[] {
    const strengths: string[] = [];
    
    if (analysis.form.recent.winRate >= 70) {
      strengths.push('Excellent recent form');
    }
    
    if (analysis.performance.openingKills >= config.metrics.minOpeningKillRate) {
      strengths.push('Strong opening kill success');
    }
    
    if (analysis.performance.clutchPerformance >= config.metrics.minClutchSuccessRate) {
      strengths.push('Reliable clutch performance');
    }
    
    if (analysis.mapPool.strongMaps.length >= 3) {
      strengths.push('Deep map pool');
    }
    
    if (analysis.roster.stability >= 90) {
      strengths.push('Stable roster');
    }
    
    if (analysis.roster.averageRating >= 1.10) {
      strengths.push('High individual skill level');
    }
    
    return strengths;
  }
  
  /**
   * Identify team weaknesses
   */
  private static identifyWeaknesses(analysis: TeamAnalysisResult): string[] {
    const weaknesses: string[] = [];
    
    if (analysis.form.recent.winRate <= 40) {
      weaknesses.push('Poor recent form');
    }
    
    if (analysis.performance.economyManagement <= 40) {
      weaknesses.push('Weak economy management');
    }
    
    if (analysis.mapPool.weakMaps.length >= 3) {
      weaknesses.push('Limited map pool');
    }
    
    if (analysis.roster.stability <= 60) {
      weaknesses.push('Roster instability');
    }
    
    if (analysis.dataQuality.reliability === 'low') {
      weaknesses.push('Insufficient data for reliable analysis');
    }
    
    return weaknesses;
  }
}
