"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationUtils = void 0;
class ValidationUtils {
    /**
     * Validate match data completeness and quality
     */
    static validateMatch(match) {
        const errors = [];
        const warnings = [];
        let completeness = 0;
        const totalFields = 10;
        // Required fields
        if (!match.id)
            errors.push('Match ID is required');
        else
            completeness++;
        if (!match.date)
            errors.push('Match date is required');
        else
            completeness++;
        if (!match.team1?.id || !match.team1?.name) {
            errors.push('Team 1 information is incomplete');
        }
        else {
            completeness++;
        }
        if (!match.team2?.id || !match.team2?.name) {
            errors.push('Team 2 information is incomplete');
        }
        else {
            completeness++;
        }
        if (!match.format?.type) {
            errors.push('Match format is required');
        }
        else {
            completeness++;
        }
        if (!match.event?.id || !match.event?.name) {
            warnings.push('Event information is incomplete');
        }
        else {
            completeness++;
        }
        // Optional but important fields
        if (!match.team1.ranking) {
            warnings.push('Team 1 ranking not available');
        }
        else {
            completeness++;
        }
        if (!match.team2.ranking) {
            warnings.push('Team 2 ranking not available');
        }
        else {
            completeness++;
        }
        if (!match.odds) {
            warnings.push('Betting odds not available');
        }
        else {
            completeness++;
        }
        if (!match.hasLineup) {
            warnings.push('Team lineups not confirmed');
        }
        else {
            completeness++;
        }
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            completeness: Math.round((completeness / totalFields) * 100),
        };
    }
    /**
     * Validate team statistics data
     */
    static validateTeamStats(teamStats) {
        const errors = [];
        const warnings = [];
        let completeness = 0;
        const totalFields = 15;
        // Basic info
        if (!teamStats.teamId)
            errors.push('Team ID is required');
        else
            completeness++;
        if (!teamStats.name)
            errors.push('Team name is required');
        else
            completeness++;
        if (teamStats.ranking <= 0)
            warnings.push('Team ranking not available');
        else
            completeness++;
        if (teamStats.rating <= 0)
            warnings.push('Team rating not available');
        else
            completeness++;
        // Win/Loss records
        if (teamStats.wins < 0 || teamStats.losses < 0) {
            errors.push('Invalid win/loss record');
        }
        else {
            completeness++;
        }
        // Recent form
        if (!teamStats.recentForm?.matches?.length) {
            warnings.push('Recent form data not available');
        }
        else if (teamStats.recentForm.matches.length < 5) {
            warnings.push('Insufficient recent form data (less than 5 matches)');
            completeness += 0.5;
        }
        else {
            completeness++;
        }
        // Map statistics
        if (!teamStats.mapStats?.length) {
            warnings.push('Map statistics not available');
        }
        else {
            completeness++;
        }
        // Performance metrics
        const performance = teamStats.performance;
        if (!performance) {
            warnings.push('Performance metrics not available');
        }
        else {
            if (performance.openingKillRate >= 0)
                completeness += 0.5;
            if (performance.tradeKillEfficiency >= 0)
                completeness += 0.5;
            if (performance.economyManagement)
                completeness += 0.5;
            if (performance.clutchPerformance)
                completeness += 0.5;
            if (performance.pistolRounds)
                completeness += 0.5;
            if (performance.postPlantSituations)
                completeness += 0.5;
            if (performance.retakeSituations)
                completeness += 0.5;
        }
        // Roster information
        if (!teamStats.roster?.players?.length) {
            warnings.push('Roster information not available');
        }
        else if (teamStats.roster.players.length < 5) {
            warnings.push('Incomplete roster information');
            completeness += 0.5;
        }
        else {
            completeness++;
        }
        // Data quality
        if (!teamStats.dataQuality) {
            warnings.push('Data quality metrics not available');
        }
        else {
            completeness++;
            if (teamStats.dataQuality.sampleSize < 10) {
                warnings.push('Small sample size for statistics');
            }
        }
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            completeness: Math.round((completeness / totalFields) * 100),
        };
    }
    /**
     * Validate player statistics data
     */
    static validatePlayerStats(playerStats) {
        const errors = [];
        const warnings = [];
        let completeness = 0;
        const totalFields = 12;
        // Basic info
        if (!playerStats.playerId)
            errors.push('Player ID is required');
        else
            completeness++;
        if (!playerStats.name || !playerStats.nickname) {
            errors.push('Player name/nickname is required');
        }
        else {
            completeness++;
        }
        if (!playerStats.team?.id)
            warnings.push('Team information not available');
        else
            completeness++;
        // Basic statistics
        if (playerStats.rating <= 0)
            warnings.push('Player rating not available');
        else
            completeness++;
        if (playerStats.kpr < 0)
            warnings.push('KPR statistic not available');
        else
            completeness++;
        if (playerStats.adr < 0)
            warnings.push('ADR statistic not available');
        else
            completeness++;
        if (playerStats.kast < 0)
            warnings.push('KAST statistic not available');
        else
            completeness++;
        // Role and performance
        if (!playerStats.role)
            warnings.push('Player role not specified');
        else
            completeness++;
        if (!playerStats.rolePerformance) {
            warnings.push('Role-specific performance data not available');
        }
        else {
            completeness++;
        }
        // Form and trends
        if (!playerStats.form) {
            warnings.push('Form data not available');
        }
        else {
            completeness++;
        }
        // Situational performance
        if (!playerStats.situational) {
            warnings.push('Situational performance data not available');
        }
        else {
            completeness++;
        }
        // Data quality
        if (!playerStats.dataQuality) {
            warnings.push('Data quality metrics not available');
        }
        else {
            completeness++;
            if (playerStats.dataQuality.sampleSize < 10) {
                warnings.push('Small sample size for player statistics');
            }
        }
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            completeness: Math.round((completeness / totalFields) * 100),
        };
    }
    /**
     * Validate betting opportunity
     */
    static validateBettingOpportunity(opportunity) {
        const errors = [];
        const warnings = [];
        let completeness = 0;
        const totalFields = 8;
        // Basic info
        if (!opportunity.match?.id)
            errors.push('Match information is required');
        else
            completeness++;
        if (!opportunity.bet?.type || !opportunity.bet?.odds) {
            errors.push('Bet information is incomplete');
        }
        else {
            completeness++;
        }
        if (!opportunity.risk?.level)
            errors.push('Risk level is required');
        else
            completeness++;
        // Analysis
        if (!opportunity.analysis) {
            errors.push('Analysis is required');
        }
        else {
            if (opportunity.analysis.confidence < 60 || opportunity.analysis.confidence > 90) {
                warnings.push('Confidence level outside recommended range (60-90%)');
            }
            completeness++;
            if (opportunity.analysis.expectedValue <= 0) {
                warnings.push('Negative expected value detected');
            }
            completeness++;
            if (!opportunity.analysis.justification) {
                warnings.push('Analysis justification missing');
            }
            else {
                completeness++;
            }
        }
        // Data quality
        if (!opportunity.dataQuality) {
            warnings.push('Data quality assessment missing');
        }
        else {
            completeness++;
            if (opportunity.dataQuality.completeness < 70) {
                warnings.push('Low data completeness for analysis');
            }
        }
        // Additional checks
        if (opportunity.bet.odds < 1.01) {
            warnings.push('Extremely low odds detected');
        }
        if (opportunity.bet.odds > 10.0) {
            warnings.push('Very high odds detected - high risk');
        }
        completeness++; // For overall structure
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            completeness: Math.round((completeness / totalFields) * 100),
        };
    }
    /**
     * Validate odds format and reasonableness
     */
    static validateOdds(odds) {
        const errors = [];
        const warnings = [];
        if (odds <= 1.0) {
            errors.push('Odds must be greater than 1.0');
        }
        else if (odds < 1.01) {
            warnings.push('Extremely low odds - minimal profit potential');
        }
        else if (odds > 50.0) {
            warnings.push('Extremely high odds - very unlikely outcome');
        }
        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            completeness: 100,
        };
    }
}
exports.ValidationUtils = ValidationUtils;
//# sourceMappingURL=validation.js.map