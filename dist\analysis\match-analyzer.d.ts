import { TeamAnalysisResult } from './team-analyzer';
import { RiskAssessmentResult } from './risk-assessor';
import { EVCalculationResult } from './ev-calculator';
import { AnalysisReport } from '@/models/betting';
export interface MatchAnalysisContext {
    match: any;
    team1Analysis: TeamAnalysisResult;
    team2Analysis: TeamAnalysisResult;
    riskAssessment: RiskAssessmentResult;
    evCalculations: EVCalculationResult[];
}
export declare class MatchAnalyzer {
    private dataFetcher;
    constructor();
    /**
     * Analyze all matches for a specific date
     */
    analyzeMatchesForDate(date: Date): Promise<AnalysisReport>;
    /**
     * Analyze a single match
     */
    analyzeMatch(matchId: number): Promise<MatchAnalysisContext>;
    /**
     * Generate betting opportunities from match analysis
     */
    private generateBettingOpportunities;
    /**
     * Filter and rank betting opportunities
     */
    private filterAndRankOpportunities;
    /**
     * Generate mock odds for demonstration
     */
    private generateMockOdds;
    /**
     * Create empty report when no matches found
     */
    private createEmptyReport;
    private summarizeMapPoolAdvantage;
    private summarizeKeyPlayerMatchups;
    private summarizeAdvancedMetrics;
    private summarizeTournamentContext;
    /**
     * Generate comprehensive analysis report
     */
    private generateAnalysisReport;
    private identifyMarketInefficiencies;
    private identifyMissingDataPoints;
    private generateDataRecommendations;
    private generateWarnings;
}
//# sourceMappingURL=match-analyzer.d.ts.map