import { format, addDays, startOfDay, endOfDay, parseISO, isValid } from 'date-fns';

export type DateMode = 'today' | 'tomorrow' | 'custom';

export class DateUtils {
  /**
   * Get date based on mode
   */
  static getAnalysisDate(mode: DateMode, customDate?: string): Date {
    const now = new Date();

    switch (mode) {
      case 'today':
        return startOfDay(now);

      case 'tomorrow':
        return startOfDay(addDays(now, 1));

      case 'custom':
        if (!customDate) {
          throw new Error('Custom date string required for custom mode');
        }

        const parsed = parseISO(customDate);
        if (!isValid(parsed)) {
          throw new Error(`Invalid date format: ${customDate}. Use YYYY-MM-DD format.`);
        }

        return startOfDay(parsed);

      default:
        throw new Error(`Invalid date mode: ${mode}`);
    }
  }

  /**
   * Get date range for analysis (start and end of day)
   */
  static getDateRange(date: Date): { start: Date; end: Date } {
    return {
      start: startOfDay(date),
      end: endOfDay(date),
    };
  }

  /**
   * Format date for HLTV API calls
   */
  static formatForHLTV(date: Date): string {
    return format(date, 'yyyy-MM-dd');
  }

  /**
   * Format date for display
   */
  static formatForDisplay(date: Date): string {
    return format(date, 'yyyy-MM-dd HH:mm');
  }

  /**
   * Format date for UTC display
   */
  static formatForUTC(date: Date): string {
    return format(date, 'yyyy-MM-dd HH:mm') + ' UTC';
  }

  /**
   * Check if date is within last N days
   */
  static isWithinLastDays(date: Date, days: number): boolean {
    const now = new Date();
    const diffTime = now.getTime() - date.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= days;
  }

  /**
   * Check if date is within last N months
   */
  static isWithinLastMonths(date: Date, months: number): boolean {
    const now = new Date();
    const monthsAgo = new Date(now.getFullYear(), now.getMonth() - months, now.getDate());
    return date >= monthsAgo;
  }

  /**
   * Calculate days between two dates
   */
  static daysBetween(date1: Date, date2: Date): number {
    const diffTime = Math.abs(date2.getTime() - date1.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Get relative time string (e.g., "2 hours ago", "in 3 days")
   */
  static getRelativeTime(date: Date): string {
    const now = new Date();
    const diffMs = date.getTime() - now.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (Math.abs(diffMinutes) < 60) {
      return diffMinutes > 0 ? `in ${diffMinutes} minutes` : `${Math.abs(diffMinutes)} minutes ago`;
    } else if (Math.abs(diffHours) < 24) {
      return diffHours > 0 ? `in ${diffHours} hours` : `${Math.abs(diffHours)} hours ago`;
    } else {
      return diffDays > 0 ? `in ${diffDays} days` : `${Math.abs(diffDays)} days ago`;
    }
  }

  /**
   * Check if it's a reasonable time for analysis (not too far in future)
   */
  static isReasonableAnalysisDate(date: Date): boolean {
    const now = new Date();
    const maxFutureDays = 30; // Don't analyze matches more than 30 days in future
    const maxPastDays = 7; // Allow up to 7 days in the past

    const diffMs = date.getTime() - now.getTime();
    const diffDays = Math.abs(diffMs / (1000 * 60 * 60 * 24));

    // Allow dates from 7 days ago to 30 days in the future
    if (diffMs < 0) {
      // Date is in the past
      return diffDays <= maxPastDays;
    } else {
      // Date is in the future or today
      return diffDays <= maxFutureDays;
    }
  }

  /**
   * Parse various date string formats
   */
  static parseFlexibleDate(dateString: string): Date {
    // Try common formats
    const formats = [
      'yyyy-MM-dd',
      'MM/dd/yyyy',
      'dd/MM/yyyy',
      'yyyy-MM-dd HH:mm',
      'MM/dd/yyyy HH:mm',
    ];

    for (const formatStr of formats) {
      try {
        const parsed = parseISO(dateString);
        if (isValid(parsed)) {
          return parsed;
        }
      } catch {
        // Continue to next format
      }
    }

    throw new Error(`Unable to parse date: ${dateString}`);
  }
}
