{"version": 3, "file": "ev-calculator.js", "sourceRoot": "", "sources": ["../../src/analysis/ev-calculator.ts"], "names": [], "mappings": ";;;AAGA,mDAA+C;AAC/C,2CAAwC;AACxC,gDAA2C;AAwD3C,MAAa,YAAY;IACvB;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAC3B,OAAqB,EACrB,WAA8B;QAE9B,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;YAC1C,KAAK,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI;YACjC,KAAK,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI;YACjC,WAAW;SACZ,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;QAChG,MAAM,gBAAgB,GAAG,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;QACjG,MAAM,IAAI,GAAG,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;QAEjF,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;QAED,6BAA6B;QAC7B,MAAM,eAAe,GAAG,IAAI,CAAC,4BAA4B,CACvD,eAAe,EACf,gBAAgB,EAChB,OAAO,CACR,CAAC;QAEF,qCAAqC;QACrC,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QAErE,2BAA2B;QAC3B,MAAM,kBAAkB,GAAG,sBAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAC9D,MAAM,aAAa,GAAG,sBAAS,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QACrE,MAAM,uBAAuB,GAAG,sBAAS,CAAC,uBAAuB,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QAEzF,4BAA4B;QAC5B,MAAM,aAAa,GAAG,sBAAS,CAAC,cAAc,CAAC,eAAe,EAAE,IAAI,EAAE,iBAAM,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;QAE1G,sCAAsC;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,0BAA0B,CAAC,eAAe,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;QAC5F,MAAM,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,uBAAuB,EAAE,UAAU,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;QAEhH,OAAO;YACL,OAAO,EAAE,cAAc;YACvB,SAAS,EAAE,GAAG,eAAe,CAAC,IAAI,SAAS;YAE3C,IAAI,EAAE;gBACJ,OAAO,EAAE,IAAI;gBACb,kBAAkB,EAAE,sBAAS,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC;gBAC1D,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,MAAM,IAAI,SAAS;aAC1C;YAED,UAAU,EAAE;gBACV,eAAe,EAAE,sBAAS,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC;gBACpD,UAAU,EAAE,sBAAS,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC;gBAC1C,WAAW,EAAE;oBACX,oBAAoB;oBACpB,yBAAyB;oBACzB,qBAAqB;oBACrB,2BAA2B;oBAC3B,oBAAoB;iBACrB;aACF;YAED,aAAa,EAAE;gBACb,QAAQ,EAAE,sBAAS,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;gBAC3C,UAAU,EAAE,sBAAS,CAAC,KAAK,CAAC,uBAAuB,EAAE,CAAC,CAAC;gBACvD,UAAU,EAAE,aAAa,GAAG,CAAC;aAC9B;YAED,KAAK,EAAE;gBACL,QAAQ,EAAE,sBAAS,CAAC,KAAK,CAAC,aAAa,GAAG,iBAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC,CAAC;gBAChF,gBAAgB,EAAE,sBAAS,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,4BAA4B;gBACxF,QAAQ,EAAE,sBAAS,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;aAC5C;YAED,OAAO;YACP,cAAc;SACf,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,4BAA4B,CACzC,WAA+B,EAC/B,YAAgC,EAChC,OAAqB;QAErB,2CAA2C;QAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;QACzE,IAAI,eAAe,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,6BAA6B;QAEvF,yBAAyB;QACzB,MAAM,cAAc,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QAC1F,eAAe,IAAI,cAAc,GAAG,GAAG,CAAC,CAAC,cAAc;QAEvD,uCAAuC;QACvC,MAAM,gBAAgB,GAAG,WAAW,CAAC,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC;QAChF,eAAe,IAAI,gBAAgB,GAAG,EAAE,CAAC,CAAC,gBAAgB;QAE1D,qBAAqB;QACrB,MAAM,gBAAgB,GAAG,WAAW,CAAC,OAAO,CAAC,gBAAgB,GAAG,YAAY,CAAC,OAAO,CAAC,gBAAgB,CAAC;QACtG,eAAe,IAAI,gBAAgB,GAAG,GAAG,CAAC;QAE1C,oBAAoB;QACpB,IAAI,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,KAAK,EAAE,CAAC;YAChD,eAAe,IAAI,CAAC,CAAC,CAAC,iCAAiC;QACzD,CAAC;aAAM,IAAI,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,KAAK,EAAE,CAAC;YACvD,eAAe,IAAI,CAAC,CAAC,CAAC,mCAAmC;QAC3D,CAAC;QAED,qBAAqB;QACrB,MAAM,eAAe,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QACnG,eAAe,IAAI,eAAe,GAAG,GAAG,CAAC;QAEzC,0BAA0B;QAC1B,IAAI,WAAW,CAAC,MAAM,CAAC,SAAS,GAAG,EAAE,EAAE,CAAC;YACtC,eAAe,IAAI,CAAC,CAAC;QACvB,CAAC;QAED,mBAAmB;QACnB,IAAI,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,WAAW;YAAE,eAAe,IAAI,CAAC,CAAC;QACxE,IAAI,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,WAAW;YAAE,eAAe,IAAI,CAAC,CAAC;QACxE,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,WAAW;YAAE,eAAe,IAAI,CAAC,CAAC;QACzE,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,WAAW;YAAE,eAAe,IAAI,CAAC,CAAC;QAEzE,6BAA6B;QAC7B,OAAO,sBAAS,CAAC,KAAK,CAAC,eAAe,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAC3B,OAAqB,EACrB,IAAY,EACZ,IAAY,EACZ,WAA8B;QAE9B,MAAM,eAAe,GAAG,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;QAChG,MAAM,gBAAgB,GAAG,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;QAEjG,6CAA6C;QAC7C,MAAM,eAAe,GAAG,IAAI,CAAC,4BAA4B,CACvD,eAAe,EACf,gBAAgB,EAChB,IAAI,EACJ,OAAO,CACR,CAAC;QAEF,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QACrE,MAAM,kBAAkB,GAAG,sBAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAC9D,MAAM,aAAa,GAAG,sBAAS,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QACrE,MAAM,uBAAuB,GAAG,sBAAS,CAAC,uBAAuB,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QAEzF,MAAM,aAAa,GAAG,sBAAS,CAAC,cAAc,CAAC,eAAe,EAAE,IAAI,EAAE,iBAAM,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;QAE1G,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,eAAe,EAAE,gBAAgB,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAC/F,MAAM,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,uBAAuB,EAAE,UAAU,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;QAEhH,OAAO;YACL,OAAO,EAAE,cAAc;YACvB,SAAS,EAAE,GAAG,eAAe,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,OAAO;YAEvE,IAAI,EAAE;gBACJ,OAAO,EAAE,IAAI;gBACb,kBAAkB,EAAE,sBAAS,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC;gBAC1D,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,MAAM,IAAI,SAAS;aAC1C;YAED,UAAU,EAAE;gBACV,eAAe,EAAE,sBAAS,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,CAAC;gBACpD,UAAU,EAAE,sBAAS,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC;gBAC1C,WAAW,EAAE;oBACX,mBAAmB;oBACnB,4BAA4B;oBAC5B,wBAAwB;oBACxB,6BAA6B;iBAC9B;aACF;YAED,aAAa,EAAE;gBACb,QAAQ,EAAE,sBAAS,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;gBAC3C,UAAU,EAAE,sBAAS,CAAC,KAAK,CAAC,uBAAuB,EAAE,CAAC,CAAC;gBACvD,UAAU,EAAE,aAAa,GAAG,CAAC;aAC9B;YAED,KAAK,EAAE;gBACL,QAAQ,EAAE,sBAAS,CAAC,KAAK,CAAC,aAAa,GAAG,iBAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC,CAAC;gBAChF,gBAAgB,EAAE,sBAAS,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,EAAE,CAAC,CAAC;gBAC1D,QAAQ,EAAE,sBAAS,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;aAC5C;YAED,OAAO;YACP,cAAc;SACf,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,4BAA4B,CACzC,WAA+B,EAC/B,YAAgC,EAChC,IAAY,EACZ,OAAqB;QAErB,iDAAiD;QACjD,MAAM,YAAY,GAAG,WAAW,CAAC,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC;QAC5E,IAAI,eAAe,GAAG,EAAE,GAAG,CAAC,YAAY,GAAG,EAAE,CAAC,CAAC;QAE/C,iCAAiC;QACjC,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,gBAAgB,GAAG,YAAY,CAAC,OAAO,CAAC,gBAAgB,CAAC;QACjG,eAAe,IAAI,WAAW,GAAG,GAAG,CAAC;QAErC,8BAA8B;QAC9B,IAAI,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,KAAK,EAAE,CAAC;YAChD,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;gBAC3B,eAAe,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,oCAAoC;YAC9E,CAAC;QACH,CAAC;QAED,iCAAiC;QACjC,MAAM,eAAe,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QACnG,eAAe,IAAI,eAAe,GAAG,IAAI,CAAC;QAE1C,OAAO,sBAAS,CAAC,KAAK,CAAC,eAAe,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,OAAqB,EAAE,OAAgB;QACxE,IAAI,cAAc,GAAG,iBAAM,CAAC,QAAQ,CAAC,sBAAsB,CAAC;QAE5D,sBAAsB;QACtB,MAAM,cAAc,GAAG,CACrB,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,YAAY;YAC9C,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,YAAY,CAC/C,GAAG,CAAC,CAAC;QAEN,cAAc,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC;QAE9C,qBAAqB;QACrB,MAAM,aAAa,GAAG,CACpB,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,UAAU;YAC5C,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,UAAU,CAC7C,GAAG,CAAC,CAAC;QAEN,cAAc,IAAI,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;QAEvD,6BAA6B;QAC7B,cAAc,IAAI,OAAO,CAAC,cAAc,CAAC,UAAU,GAAG,GAAG,CAAC;QAE1D,gCAAgC;QAChC,IAAI,OAAO,KAAK,cAAc,EAAE,CAAC;YAC/B,cAAc,IAAI,CAAC,CAAC,CAAC,6CAA6C;QACpE,CAAC;QAED,OAAO,sBAAS,CAAC,KAAK,CAAC,cAAc,EAAE,iBAAM,CAAC,QAAQ,CAAC,sBAAsB,EAAE,iBAAM,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;IACzH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B,CACvC,WAA+B,EAC/B,YAAgC,EAChC,OAAqB;QAErB,MAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,cAAc,GAAa,EAAE,CAAC;QAEpC,YAAY;QACZ,IAAI,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;YAC1C,SAAS,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC/C,SAAS,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,OAAO,CAAC,cAAc,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;YAC/C,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACxC,CAAC;QAED,WAAW;QACX,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;YACnD,QAAQ,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,KAAK,EAAE,CAAC;YAChD,QAAQ,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,WAAW,CAAC,MAAM,CAAC,SAAS,GAAG,EAAE,EAAE,CAAC;YACtC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACzC,CAAC;QAED,kBAAkB;QAClB,cAAc,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC9C,cAAc,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QACpD,cAAc,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAEjD,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC;IACjD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,uBAAuB,CACpC,WAA+B,EAC/B,YAAgC,EAChC,IAAY,EACZ,OAAqB;QAErB,MAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,cAAc,GAAa,EAAE,CAAC;QAEpC,IAAI,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YAChE,SAAS,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,EAAE,EAAE,CAAC;YAC9C,SAAS,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC9C,QAAQ,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAC3D,CAAC;QAED,cAAc,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACzD,cAAc,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QAEzD,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC;IACjD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,sBAAsB,CACnC,uBAA+B,EAC/B,UAAkB,EAClB,cAAoC;QAEpC,IAAI,cAAc,CAAC,cAAc,KAAK,OAAO,EAAE,CAAC;YAC9C,OAAO;gBACL,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE,8CAA8C;gBACzD,UAAU,EAAE,UAAU;aACvB,CAAC;QACJ,CAAC;QAED,IAAI,uBAAuB,GAAG,iBAAM,CAAC,QAAQ,CAAC,sBAAsB,GAAG,GAAG,EAAE,CAAC;YAC3E,OAAO;gBACL,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE,wCAAwC;gBACnD,UAAU,EAAE,UAAU;aACvB,CAAC;QACJ,CAAC;QAED,IAAI,uBAAuB,IAAI,CAAC,IAAI,UAAU,IAAI,EAAE,IAAI,cAAc,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;YAC3F,OAAO;gBACL,MAAM,EAAE,KAAK;gBACb,SAAS,EAAE,sDAAsD;gBACjE,UAAU,EAAE,UAAU;aACvB,CAAC;QACJ,CAAC;QAED,IAAI,uBAAuB,IAAI,CAAC,IAAI,UAAU,IAAI,EAAE,EAAE,CAAC;YACrD,OAAO;gBACL,MAAM,EAAE,KAAK;gBACb,SAAS,EAAE,sCAAsC;gBACjD,UAAU,EAAE,UAAU;aACvB,CAAC;QACJ,CAAC;QAED,OAAO;YACL,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE,gDAAgD;YAC3D,UAAU,EAAE,UAAU;SACvB,CAAC;IACJ,CAAC;CACF;AApYD,oCAoYC"}