import { RiskLevel, RiskCriteria } from '@/models/betting';
import { TeamAnalysisResult } from './team-analyzer';
import { config } from '@/config/settings';
import { Logger } from '@/utils/logger';
import { MathUtils } from '@/utils/math-utils';

export interface RiskAssessmentResult {
  riskLevel: RiskLevel;
  score: number; // 0-100, higher is safer
  
  criteria: {
    rankingGap: {
      value: number;
      meets: boolean;
      weight: number;
    };
    favoredTeamWinRate: {
      value: number;
      meets: boolean;
      weight: number;
    };
    rosterStability: {
      value: number;
      meets: boolean;
      weight: number;
    };
    matchFormat: {
      value: string;
      meets: boolean;
      weight: number;
    };
    mapPoolAdvantage: {
      value: number;
      meets: boolean;
      weight: number;
    };
    teamRating: {
      value: number;
      meets: boolean;
      weight: number;
    };
    pistolWinRate: {
      value: number;
      meets: boolean;
      weight: number;
    };
  };
  
  exclusionFactors: Array<{
    factor: string;
    reason: string;
    severity: 'low' | 'medium' | 'high';
  }>;
  
  mitigatingFactors: string[];
  riskFactors: string[];
  
  recommendation: 'proceed' | 'caution' | 'avoid';
  confidence: number;
}

export interface MatchRiskContext {
  team1Analysis: TeamAnalysisResult;
  team2Analysis: TeamAnalysisResult;
  matchFormat: string;
  tournamentTier: string;
  significance: number;
}

export class RiskAssessor {
  private static readonly CRITERIA_WEIGHTS = {
    rankingGap: 0.20,
    favoredTeamWinRate: 0.18,
    rosterStability: 0.15,
    matchFormat: 0.12,
    mapPoolAdvantage: 0.12,
    teamRating: 0.13,
    pistolWinRate: 0.10,
  };
  
  /**
   * Assess risk level for a betting opportunity
   */
  static assessRisk(context: MatchRiskContext): RiskAssessmentResult {
    Logger.debug('Assessing match risk', {
      team1: context.team1Analysis.name,
      team2: context.team2Analysis.name,
      format: context.matchFormat,
    });
    
    const startTime = Date.now();
    
    try {
      // Determine favored team (lower ranking number = higher rank)
      const favoredTeam = context.team1Analysis.ranking <= context.team2Analysis.ranking 
        ? context.team1Analysis 
        : context.team2Analysis;
      
      const underdogTeam = favoredTeam === context.team1Analysis 
        ? context.team2Analysis 
        : context.team1Analysis;
      
      // Evaluate all criteria
      const criteria = this.evaluateCriteria(favoredTeam, underdogTeam, context);
      
      // Check for exclusion factors
      const exclusionFactors = this.checkExclusionFactors(favoredTeam, underdogTeam, context);
      
      // Calculate risk score
      const score = this.calculateRiskScore(criteria);
      
      // Determine risk level
      const riskLevel = this.determineRiskLevel(criteria, exclusionFactors, score);
      
      // Identify mitigating and risk factors
      const mitigatingFactors = this.identifyMitigatingFactors(favoredTeam, underdogTeam, context);
      const riskFactors = this.identifyRiskFactors(favoredTeam, underdogTeam, context);
      
      // Generate recommendation
      const recommendation = this.generateRecommendation(riskLevel, exclusionFactors, score);
      
      // Calculate confidence in assessment
      const confidence = this.calculateAssessmentConfidence(favoredTeam, underdogTeam);
      
      const result: RiskAssessmentResult = {
        riskLevel,
        score: MathUtils.round(score, 1),
        criteria,
        exclusionFactors,
        mitigatingFactors,
        riskFactors,
        recommendation,
        confidence: MathUtils.round(confidence, 1),
      };
      
      const duration = Date.now() - startTime;
      Logger.performance('risk_assessment', duration, {
        team1: context.team1Analysis.name,
        team2: context.team2Analysis.name,
        riskLevel: result.riskLevel,
        score: result.score,
      });
      
      return result;
    } catch (error) {
      Logger.error('Risk assessment failed', error as Error, {
        team1: context.team1Analysis.name,
        team2: context.team2Analysis.name,
      });
      throw error;
    }
  }
  
  /**
   * Evaluate all risk criteria
   */
  private static evaluateCriteria(
    favoredTeam: TeamAnalysisResult,
    underdogTeam: TeamAnalysisResult,
    context: MatchRiskContext
  ): RiskAssessmentResult['criteria'] {
    const rankingGap = Math.abs(favoredTeam.ranking - underdogTeam.ranking);
    
    return {
      rankingGap: {
        value: rankingGap,
        meets: rankingGap >= config.risk.medium.rankingGap,
        weight: this.CRITERIA_WEIGHTS.rankingGap,
      },
      
      favoredTeamWinRate: {
        value: favoredTeam.form.recent.winRate,
        meets: favoredTeam.form.recent.winRate >= config.risk.medium.favoredTeamWinRate,
        weight: this.CRITERIA_WEIGHTS.favoredTeamWinRate,
      },
      
      rosterStability: {
        value: favoredTeam.roster.stability,
        meets: favoredTeam.roster.stability >= 80, // 80% stability threshold
        weight: this.CRITERIA_WEIGHTS.rosterStability,
      },
      
      matchFormat: {
        value: context.matchFormat,
        meets: ['bo3', 'bo5'].includes(context.matchFormat.toLowerCase()),
        weight: this.CRITERIA_WEIGHTS.matchFormat,
      },
      
      mapPoolAdvantage: {
        value: favoredTeam.mapPool.overallAdvantage,
        meets: favoredTeam.mapPool.overallAdvantage >= 10, // 10% advantage
        weight: this.CRITERIA_WEIGHTS.mapPoolAdvantage,
      },
      
      teamRating: {
        value: favoredTeam.overallRating,
        meets: favoredTeam.overallRating >= config.risk.medium.teamRating,
        weight: this.CRITERIA_WEIGHTS.teamRating,
      },
      
      pistolWinRate: {
        value: favoredTeam.performance.pistolRounds,
        meets: favoredTeam.performance.pistolRounds >= config.risk.medium.pistolWinRate,
        weight: this.CRITERIA_WEIGHTS.pistolWinRate,
      },
    };
  }
  
  /**
   * Check for exclusion factors that would disqualify the bet
   */
  private static checkExclusionFactors(
    favoredTeam: TeamAnalysisResult,
    underdogTeam: TeamAnalysisResult,
    context: MatchRiskContext
  ): RiskAssessmentResult['exclusionFactors'] {
    const factors: RiskAssessmentResult['exclusionFactors'] = [];
    
    // Low win rate exclusion
    if (favoredTeam.form.recent.winRate < config.risk.exclusion.minWinRate) {
      factors.push({
        factor: 'Low recent win rate',
        reason: `Favored team win rate (${favoredTeam.form.recent.winRate}%) below minimum threshold`,
        severity: 'high',
      });
    }
    
    // Roster instability exclusion
    if (favoredTeam.roster.stability < 60) {
      factors.push({
        factor: 'Roster instability',
        reason: `Recent roster changes detected (${favoredTeam.roster.stability}% stability)`,
        severity: 'medium',
      });
    }
    
    // Low individual ratings
    if (favoredTeam.roster.averageRating < config.risk.exclusion.minPlayerRating) {
      factors.push({
        factor: 'Low player ratings',
        reason: `Team average rating (${favoredTeam.roster.averageRating}) below threshold`,
        severity: 'medium',
      });
    }
    
    // Problematic format
    if (context.matchFormat.toLowerCase() === 'bo1' && favoredTeam.mapPool.strongMaps.length < 2) {
      factors.push({
        factor: 'Risky format',
        reason: 'BO1 format with limited map pool advantage',
        severity: 'high',
      });
    }
    
    // Insufficient data
    if (favoredTeam.dataQuality.reliability === 'low' || underdogTeam.dataQuality.reliability === 'low') {
      factors.push({
        factor: 'Insufficient data',
        reason: 'Low data quality for reliable analysis',
        severity: 'medium',
      });
    }
    
    return factors;
  }
  
  /**
   * Calculate overall risk score
   */
  private static calculateRiskScore(criteria: RiskAssessmentResult['criteria']): number {
    let totalScore = 0;
    let totalWeight = 0;
    
    Object.values(criteria).forEach(criterion => {
      const score = criterion.meets ? 100 : 0;
      totalScore += score * criterion.weight;
      totalWeight += criterion.weight;
    });
    
    return totalWeight > 0 ? totalScore / totalWeight : 0;
  }
  
  /**
   * Determine risk level based on criteria and exclusions
   */
  private static determineRiskLevel(
    criteria: RiskAssessmentResult['criteria'],
    exclusionFactors: RiskAssessmentResult['exclusionFactors'],
    score: number
  ): RiskLevel {
    // High severity exclusions = high risk
    const highSeverityExclusions = exclusionFactors.filter(f => f.severity === 'high');
    if (highSeverityExclusions.length > 0) {
      return 'high';
    }
    
    // Check for low risk criteria (all must be met)
    const lowRiskCriteria = [
      criteria.rankingGap.value >= config.risk.low.rankingGap,
      criteria.favoredTeamWinRate.value >= config.risk.low.favoredTeamWinRate,
      criteria.rosterStability.meets,
      criteria.matchFormat.meets,
      criteria.mapPoolAdvantage.value >= config.risk.low.mapPoolAdvantage,
      criteria.teamRating.value >= config.risk.low.teamRating,
      criteria.pistolWinRate.value >= config.risk.low.pistolWinRate,
    ];
    
    const allLowRiskMet = lowRiskCriteria.every(Boolean);
    const noMediumSeverityExclusions = exclusionFactors.filter(f => f.severity === 'medium').length === 0;
    
    if (allLowRiskMet && noMediumSeverityExclusions && score >= 85) {
      return 'low';
    }
    
    // Check for medium risk criteria
    const mediumRiskCriteria = [
      criteria.rankingGap.value >= config.risk.medium.rankingGap,
      criteria.favoredTeamWinRate.value >= config.risk.medium.favoredTeamWinRate,
      criteria.teamRating.value >= config.risk.medium.teamRating,
    ];
    
    const mediumRiskMet = mediumRiskCriteria.filter(Boolean).length >= 2;
    
    if (mediumRiskMet && score >= 60) {
      return 'medium';
    }
    
    return 'high';
  }
  
  /**
   * Identify mitigating factors that reduce risk
   */
  private static identifyMitigatingFactors(
    favoredTeam: TeamAnalysisResult,
    underdogTeam: TeamAnalysisResult,
    context: MatchRiskContext
  ): string[] {
    const factors: string[] = [];
    
    if (favoredTeam.form.recent.trend === 'improving') {
      factors.push('Favored team showing improving form');
    }
    
    if (favoredTeam.mapPool.strongMaps.length >= 3) {
      factors.push('Strong map pool depth');
    }
    
    if (favoredTeam.roster.keyPlayers.length >= 2) {
      factors.push('Multiple key players performing well');
    }
    
    if (underdogTeam.form.recent.trend === 'declining') {
      factors.push('Underdog team in declining form');
    }
    
    if (context.significance >= 4) {
      factors.push('High-stakes match favoring experienced team');
    }
    
    if (favoredTeam.contextualFactors.motivationLevel >= 70) {
      factors.push('High motivation level for favored team');
    }
    
    return factors;
  }
  
  /**
   * Identify risk factors that increase risk
   */
  private static identifyRiskFactors(
    favoredTeam: TeamAnalysisResult,
    underdogTeam: TeamAnalysisResult,
    context: MatchRiskContext
  ): string[] {
    const factors: string[] = [];
    
    if (favoredTeam.form.recent.consistency < 60) {
      factors.push('Inconsistent recent performance');
    }
    
    if (underdogTeam.form.recent.trend === 'improving') {
      factors.push('Underdog team showing improvement');
    }
    
    if (context.matchFormat.toLowerCase() === 'bo1') {
      factors.push('BO1 format increases variance');
    }
    
    if (favoredTeam.contextualFactors.travelImpact > 50) {
      factors.push('Potential travel fatigue impact');
    }
    
    if (favoredTeam.weaknesses.includes('Poor recent form')) {
      factors.push('Recent form concerns');
    }
    
    if (underdogTeam.strengths.includes('Strong opening kill success')) {
      factors.push('Underdog has strong tempo control');
    }
    
    return factors;
  }
  
  /**
   * Generate betting recommendation
   */
  private static generateRecommendation(
    riskLevel: RiskLevel,
    exclusionFactors: RiskAssessmentResult['exclusionFactors'],
    score: number
  ): 'proceed' | 'caution' | 'avoid' {
    const highSeverityExclusions = exclusionFactors.filter(f => f.severity === 'high').length;
    
    if (highSeverityExclusions > 0 || riskLevel === 'high') {
      return 'avoid';
    }
    
    if (riskLevel === 'low' && score >= 80) {
      return 'proceed';
    }
    
    return 'caution';
  }
  
  /**
   * Calculate confidence in risk assessment
   */
  private static calculateAssessmentConfidence(
    favoredTeam: TeamAnalysisResult,
    underdogTeam: TeamAnalysisResult
  ): number {
    const factors = [
      favoredTeam.dataQuality.completeness,
      underdogTeam.dataQuality.completeness,
      Math.min(favoredTeam.dataQuality.sampleSize / 20, 1) * 100, // Sample size factor
      Math.min(underdogTeam.dataQuality.sampleSize / 20, 1) * 100,
    ];
    
    const weights = [0.3, 0.3, 0.2, 0.2];
    
    return MathUtils.clamp(MathUtils.weightedAverage(factors, weights), 0, 100);
  }
  
  /**
   * Check if match meets minimum criteria for any betting consideration
   */
  static meetsMinimumCriteria(context: MatchRiskContext): boolean {
    const favoredTeam = context.team1Analysis.ranking <= context.team2Analysis.ranking 
      ? context.team1Analysis 
      : context.team2Analysis;
    
    // Basic minimum requirements
    const requirements = [
      favoredTeam.form.recent.winRate >= 50, // At least 50% recent win rate
      favoredTeam.dataQuality.reliability !== 'low', // Adequate data quality
      favoredTeam.roster.stability >= 50, // Minimum roster stability
    ];
    
    return requirements.every(Boolean);
  }
}
