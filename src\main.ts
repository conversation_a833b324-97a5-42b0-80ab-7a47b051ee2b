#!/usr/bin/env node

// Register path mapping for compiled JavaScript
import 'tsconfig-paths/register';

import { Command } from 'commander';
import chalk from 'chalk';
import { MatchA<PERSON>yzer } from '@/analysis/match-analyzer';
import { ReportGenerator, OutputFormat } from '@/services/report-generator';
import { DateUtils, DateMode } from '@/utils/date-utils';
import { Logger } from '@/utils/logger';
import { config } from '@/config/settings';
import { cacheService } from '@/services/cache-service';

const program = new Command();

// CLI Configuration
program
  .name('cs2-betting-analytics')
  .description('Ultimate CS2 betting analytics system using HLTV data')
  .version('1.0.0');

// Main analysis command
program
  .command('analyze')
  .description('Analyze CS2 matches for betting opportunities')
  .option('-d, --date <date>', 'Analysis date (today, tomorrow, or YYYY-MM-DD)', 'today')
  .option('-f, --format <format>', 'Output format (console, json, html)', 'console')
  .option('-s, --save', 'Save report to file', false)
  .option('--no-cache', 'Disable caching', false)
  .option('--force-refresh', 'Force refresh cached data', false)
  .option('--verbose', 'Enable verbose logging', false)
  .action(async (options) => {
    try {
      await runAnalysis(options);
    } catch (error) {
      console.error(chalk.red('❌ Analysis failed:'), (error as Error).message);
      process.exit(1);
    }
  });

// Cache management commands
program
  .command('cache')
  .description('Cache management operations')
  .option('--clear', 'Clear all cache')
  .option('--stats', 'Show cache statistics')
  .option('--clear-type <type>', 'Clear specific cache type (matches, teams, players, rankings)')
  .action(async (options) => {
    try {
      await manageCacheOperations(options);
    } catch (error) {
      console.error(chalk.red('❌ Cache operation failed:'), (error as Error).message);
      process.exit(1);
    }
  });

// Configuration command
program
  .command('config')
  .description('Show current configuration')
  .action(() => {
    showConfiguration();
  });

// Test command for development
program
  .command('test')
  .description('Test HLTV API connectivity')
  .action(async () => {
    try {
      await testConnectivity();
    } catch (error) {
      console.error(chalk.red('❌ Connectivity test failed:'), (error as Error).message);
      process.exit(1);
    }
  });

/**
 * Main analysis function
 */
async function runAnalysis(options: {
  date: string;
  format: string;
  save: boolean;
  cache: boolean;
  forceRefresh: boolean;
  verbose: boolean;
}): Promise<void> {
  // Setup logging
  if (options.verbose) {
    process.env.LOG_LEVEL = 'debug';
  }

  console.log(chalk.bold.cyan('🎯 CS2 Betting Analytics System'));
  console.log(chalk.gray('═'.repeat(50)));
  console.log('');

  // Parse date
  let dateMode: DateMode;
  let customDate: string | undefined;

  if (options.date === 'today') {
    dateMode = 'today';
  } else if (options.date === 'tomorrow') {
    dateMode = 'tomorrow';
  } else {
    dateMode = 'custom';
    customDate = options.date;
  }

  const analysisDate = DateUtils.getAnalysisDate(dateMode, customDate);

  // Validate date
  if (!DateUtils.isReasonableAnalysisDate(analysisDate)) {
    throw new Error('Analysis date is too far in the future or invalid');
  }

  console.log(chalk.blue('📅 Analysis Configuration:'));
  console.log(`   Date: ${chalk.white(DateUtils.formatForDisplay(analysisDate))}`);
  console.log(`   Format: ${chalk.white(options.format)}`);
  console.log(`   Cache: ${chalk.white(options.cache ? 'Enabled' : 'Disabled')}`);
  console.log(`   Force Refresh: ${chalk.white(options.forceRefresh ? 'Yes' : 'No')}`);
  console.log('');

  // Initialize analyzer
  console.log(chalk.yellow('🔄 Initializing analysis engine...'));
  const analyzer = new MatchAnalyzer();

  // Show cache stats if enabled
  if (options.cache) {
    const cacheStats = cacheService.getStats();
    console.log(chalk.gray(`   Cache: ${cacheStats.keys} keys, ${cacheStats.hitRate.toFixed(1)}% hit rate`));
  }

  console.log('');

  // Run analysis
  console.log(chalk.yellow('🚀 Starting analysis...'));
  const startTime = Date.now();

  const report = await analyzer.analyzeMatchesForDate(analysisDate);

  const duration = Date.now() - startTime;
  console.log(chalk.green(`✅ Analysis completed in ${duration}ms`));
  console.log('');

  // Generate and display report
  const outputFormat = options.format as OutputFormat;

  if (outputFormat === 'console') {
    ReportGenerator.displayReport(report);
  } else {
    const reportContent = ReportGenerator.generateReport(report, outputFormat);
    console.log(reportContent);
  }

  // Save report if requested
  if (options.save) {
    console.log(chalk.blue('💾 Saving report...'));
    const filepath = await ReportGenerator.saveReport(report, outputFormat);
    console.log(chalk.green(`📄 Report saved: ${filepath}`));
  }

  // Show final stats
  console.log('');
  console.log(chalk.gray('📊 Session Statistics:'));
  console.log(chalk.gray(`   Execution Time: ${duration}ms`));
  console.log(chalk.gray(`   API Requests: ${report.metadata.apiRequestsUsed}`));
  console.log(chalk.gray(`   Opportunities Found: ${report.opportunities.length}`));

  if (options.cache) {
    const finalCacheStats = cacheService.getStats();
    console.log(chalk.gray(`   Cache Hit Rate: ${finalCacheStats.hitRate.toFixed(1)}%`));
  }
}

/**
 * Cache management operations
 */
async function manageCacheOperations(options: {
  clear?: boolean;
  stats?: boolean;
  clearType?: string;
}): Promise<void> {
  console.log(chalk.bold.blue('🗄️  Cache Management'));
  console.log(chalk.gray('═'.repeat(30)));
  console.log('');

  if (options.stats) {
    const stats = cacheService.getStats();
    console.log(chalk.yellow('📊 Cache Statistics:'));
    console.log(`   Total Keys: ${chalk.white(stats.keys)}`);
    console.log(`   Cache Hits: ${chalk.white(stats.hits)}`);
    console.log(`   Cache Misses: ${chalk.white(stats.misses)}`);
    console.log(`   Hit Rate: ${chalk.white(stats.hitRate.toFixed(2))}%`);
    console.log(`   Memory Size: ${chalk.white(Math.round(stats.size / 1024))} KB`);
  }

  if (options.clear) {
    cacheService.clearAll();
    console.log(chalk.green('✅ All cache cleared'));
  }

  if (options.clearType) {
    const validTypes = ['matches', 'teams', 'players', 'rankings', 'team_stats', 'player_stats', 'match_details', 'events'];

    if (!validTypes.includes(options.clearType)) {
      throw new Error(`Invalid cache type. Valid types: ${validTypes.join(', ')}`);
    }

    cacheService.clearType(options.clearType as any);
    console.log(chalk.green(`✅ Cache type '${options.clearType}' cleared`));
  }
}

/**
 * Show current configuration
 */
function showConfiguration(): void {
  console.log(chalk.bold.blue('⚙️  Current Configuration'));
  console.log(chalk.gray('═'.repeat(40)));
  console.log('');

  console.log(chalk.yellow('🌐 HLTV API Settings:'));
  console.log(`   Request Delay: ${chalk.white(config.hltv.requestDelay)}ms`);
  console.log(`   Max Concurrent: ${chalk.white(config.hltv.maxConcurrentRequests)}`);
  console.log(`   Retry Attempts: ${chalk.white(config.hltv.retryAttempts)}`);
  console.log('');

  console.log(chalk.yellow('🗄️  Cache Settings:'));
  console.log(`   Matches TTL: ${chalk.white(config.cache.ttl.matches / 1000)}s`);
  console.log(`   Teams TTL: ${chalk.white(config.cache.ttl.teams / 1000)}s`);
  console.log(`   Players TTL: ${chalk.white(config.cache.ttl.players / 1000)}s`);
  console.log(`   Rankings TTL: ${chalk.white(config.cache.ttl.rankings / 1000)}s`);
  console.log('');

  console.log(chalk.yellow('🎯 Analysis Settings:'));
  console.log(`   Min Confidence: ${chalk.white(config.analysis.minConfidenceThreshold)}%`);
  console.log(`   Max Confidence: ${chalk.white(config.analysis.maxConfidenceThreshold)}%`);
  console.log(`   Min Positive EV: ${chalk.white(config.analysis.minPositiveEvThreshold * 100)}%`);
  console.log(`   Default Stake: $${chalk.white(config.analysis.defaultStakeAmount)}`);
  console.log('');

  console.log(chalk.yellow('⚠️  Risk Thresholds:'));
  console.log(`   Low Risk Ranking Gap: ${chalk.white(config.risk.low.rankingGap)}`);
  console.log(`   Medium Risk Ranking Gap: ${chalk.white(config.risk.medium.rankingGap)}`);
  console.log(`   Min Win Rate (Low): ${chalk.white(config.risk.low.favoredTeamWinRate)}%`);
  console.log(`   Min Win Rate (Medium): ${chalk.white(config.risk.medium.favoredTeamWinRate)}%`);
  console.log('');

  console.log(chalk.yellow('📁 Output Settings:'));
  console.log(`   Format: ${chalk.white(config.output.format)}`);
  console.log(`   Save Reports: ${chalk.white(config.output.saveReports ? 'Yes' : 'No')}`);
  console.log(`   Reports Directory: ${chalk.white(config.output.reportsDir)}`);
}

/**
 * Test HLTV API connectivity
 */
async function testConnectivity(): Promise<void> {
  console.log(chalk.bold.blue('🔌 Testing HLTV API Connectivity'));
  console.log(chalk.gray('═'.repeat(40)));
  console.log('');

  const { HLTVClient } = await import('@/api/hltv-client');
  const client = new HLTVClient();

  try {
    console.log(chalk.yellow('📡 Testing team rankings endpoint...'));
    const rankings = await client.getTeamRanking();
    console.log(chalk.green(`✅ Team rankings: ${rankings.length} teams fetched`));

    console.log(chalk.yellow('📡 Testing matches endpoint...'));
    const matches = await client.getMatches();
    console.log(chalk.green(`✅ Matches: ${matches.length} matches fetched`));

    console.log('');
    console.log(chalk.green('🎉 All connectivity tests passed!'));

    const stats = client.getRequestStats();
    console.log(chalk.gray(`📊 Requests made: ${stats.count}`));

  } catch (error) {
    console.log(chalk.red('❌ Connectivity test failed'));
    throw error;
  }
}

/**
 * Handle uncaught errors
 */
process.on('uncaughtException', (error) => {
  Logger.error('Uncaught exception', error);
  console.error(chalk.red('💥 Fatal error:'), error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  Logger.error('Unhandled rejection', new Error(String(reason)), { promise });
  console.error(chalk.red('💥 Unhandled promise rejection:'), reason);
  process.exit(1);
});

// Parse command line arguments
program.parse();

// If no command provided, show help
if (!process.argv.slice(2).length) {
  program.outputHelp();
}
