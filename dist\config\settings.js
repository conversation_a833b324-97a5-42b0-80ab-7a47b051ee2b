"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const getEnvNumber = (key, defaultValue) => {
    const value = process.env[key];
    return value ? parseInt(value, 10) : defaultValue;
};
const getEnvString = (key, defaultValue) => {
    return process.env[key] || defaultValue;
};
const getEnvBoolean = (key, defaultValue) => {
    const value = process.env[key];
    return value ? value.toLowerCase() === 'true' : defaultValue;
};
exports.config = {
    hltv: {
        requestDelay: getEnvNumber('HLTV_REQUEST_DELAY', 1000),
        maxConcurrentRequests: getEnvNumber('HLTV_MAX_CONCURRENT_REQUESTS', 3),
        retryAttempts: getEnvNumber('HLTV_RETRY_ATTEMPTS', 3),
        retryDelay: getEnvNumber('HLTV_RETRY_DELAY', 2000),
    },
    cache: {
        ttl: {
            matches: getEnvNumber('CACHE_TTL_MATCHES', 300000), // 5 minutes
            teams: getEnvNumber('CACHE_TTL_TEAMS', 1800000), // 30 minutes
            players: getEnvNumber('CACHE_TTL_PLAYERS', 3600000), // 1 hour
            rankings: getEnvNumber('CACHE_TTL_RANKINGS', 7200000), // 2 hours
        },
    },
    analysis: {
        minConfidenceThreshold: getEnvNumber('MIN_CONFIDENCE_THRESHOLD', 60),
        maxConfidenceThreshold: getEnvNumber('MAX_CONFIDENCE_THRESHOLD', 90),
        defaultStakeAmount: getEnvNumber('DEFAULT_STAKE_AMOUNT', 100),
        minPositiveEvThreshold: getEnvNumber('MIN_POSITIVE_EV_THRESHOLD', 5) / 100,
    },
    logging: {
        level: getEnvString('LOG_LEVEL', 'info'),
        filePath: getEnvString('LOG_FILE_PATH', './logs/cs2-betting.log'),
    },
    output: {
        format: getEnvString('OUTPUT_FORMAT', 'console'),
        saveReports: getEnvBoolean('SAVE_REPORTS', true),
        reportsDir: getEnvString('REPORTS_DIR', './reports'),
    },
    risk: {
        low: {
            rankingGap: getEnvNumber('LOW_RISK_RANKING_GAP', 10),
            favoredTeamWinRate: getEnvNumber('MIN_WIN_RATE_LOW_RISK', 70),
            rosterStability: 3, // 3 months
            formatRequirement: ['bo3', 'bo5'],
            mapPoolAdvantage: 65,
            teamRating: getEnvNumber('MIN_TEAM_RATING_LOW_RISK', 110) / 100,
            pistolWinRate: 55,
            additionalRequirements: [
                'No reported team issues',
                'Stable roster for 3+ months',
                'Clear map pool advantage'
            ],
        },
        medium: {
            rankingGap: getEnvNumber('MEDIUM_RISK_RANKING_GAP', 5),
            favoredTeamWinRate: getEnvNumber('MIN_WIN_RATE_MEDIUM_RISK', 60),
            rosterStability: 1, // 1 month
            formatPreference: ['bo3', 'bo5'],
            mapPoolAdvantage: 60,
            teamRating: getEnvNumber('MIN_TEAM_RATING_MEDIUM_RISK', 105) / 100,
            pistolWinRate: 50,
        },
        exclusion: {
            minWinRate: 55,
            maxRosterChangeWindow: 30, // days
            minPlayerRating: 0.95,
            problematicFormats: ['bo1'],
        },
    },
    metrics: {
        minOpeningKillRate: getEnvNumber('MIN_OPENING_KILL_RATE', 55),
        minTradeKillEfficiency: getEnvNumber('MIN_TRADE_KILL_EFFICIENCY', 70),
        minEconomyWinRate: getEnvNumber('MIN_ECONOMY_WIN_RATE', 55),
        minClutchSuccessRate: getEnvNumber('MIN_CLUTCH_SUCCESS_RATE', 30),
        minPistolWinRate: getEnvNumber('MIN_PISTOL_WIN_RATE', 50),
    },
};
exports.default = exports.config;
//# sourceMappingURL=settings.js.map