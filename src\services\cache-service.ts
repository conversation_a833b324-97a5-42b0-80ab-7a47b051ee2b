import NodeCache from 'node-cache';
import { config } from '@/config/settings';
import { Logger } from '@/utils/logger';

export type CacheKey =
  | 'matches'
  | 'teams'
  | 'players'
  | 'rankings'
  | 'team_stats'
  | 'player_stats'
  | 'match_details'
  | 'events';

export interface CacheOptions {
  ttl?: number;
  checkperiod?: number;
}

export class CacheService {
  private cache: NodeCache;
  private hitCount: number = 0;
  private missCount: number = 0;

  constructor(options?: CacheOptions) {
    this.cache = new NodeCache({
      stdTTL: 600, // Default 10 minutes
      checkperiod: 120, // Check for expired keys every 2 minutes
      useClones: false, // Don't clone objects for better performance
      ...options,
    });

    // Log cache events
    this.cache.on('set', (key, value) => {
      Logger.cacheOperation('set', key);
    });

    this.cache.on('expired', (key, value) => {
      Logger.debug('Cache key expired', { key });
    });

    this.cache.on('del', (key, value) => {
      Logger.debug('Cache key deleted', { key });
    });

    Logger.info('Cache service initialized');
  }

  /**
   * Get TTL for specific cache type
   */
  private getTTL(cacheType: CacheKey): number {
    switch (cacheType) {
      case 'matches':
        return config.cache.ttl.matches / 1000; // Convert to seconds
      case 'teams':
        return config.cache.ttl.teams / 1000;
      case 'players':
        return config.cache.ttl.players / 1000;
      case 'rankings':
        return config.cache.ttl.rankings / 1000;
      case 'team_stats':
        return config.cache.ttl.teams / 1000;
      case 'player_stats':
        return config.cache.ttl.players / 1000;
      case 'match_details':
        return config.cache.ttl.matches / 1000;
      case 'events':
        return config.cache.ttl.teams / 1000;
      default:
        return 600; // Default 10 minutes
    }
  }

  /**
   * Generate cache key
   */
  private generateKey(cacheType: CacheKey, identifier: string | number, params?: Record<string, any>): string {
    let key = `${cacheType}:${identifier}`;

    if (params) {
      const sortedParams = Object.keys(params)
        .sort()
        .map(k => `${k}=${JSON.stringify(params[k])}`)
        .join('&');

      if (sortedParams) {
        key += `:${sortedParams}`;
      }
    }

    return key;
  }

  /**
   * Get value from cache
   */
  get<T>(cacheType: CacheKey, identifier: string | number, params?: Record<string, any>): T | undefined {
    const key = this.generateKey(cacheType, identifier, params);
    const value = this.cache.get<T>(key);

    if (value !== undefined) {
      this.hitCount++;
      Logger.cacheOperation('hit', key);
      return value;
    } else {
      this.missCount++;
      Logger.cacheOperation('miss', key);
      return undefined;
    }
  }

  /**
   * Set value in cache
   */
  set<T>(
    cacheType: CacheKey,
    identifier: string | number,
    value: T,
    params?: Record<string, any>,
    customTTL?: number
  ): boolean {
    const key = this.generateKey(cacheType, identifier, params);
    const ttl = customTTL || this.getTTL(cacheType);

    const success = this.cache.set(key, value, ttl);

    if (success) {
      Logger.debug('Cache set successful', {
        key,
        ttl,
        size: JSON.stringify(value).length,
      });
    } else {
      Logger.warn('Cache set failed', { key });
    }

    return success;
  }

  /**
   * Delete value from cache
   */
  delete(cacheType: CacheKey, identifier: string | number, params?: Record<string, any>): number {
    const key = this.generateKey(cacheType, identifier, params);
    return this.cache.del(key);
  }

  /**
   * Check if key exists in cache
   */
  has(cacheType: CacheKey, identifier: string | number, params?: Record<string, any>): boolean {
    const key = this.generateKey(cacheType, identifier, params);
    return this.cache.has(key);
  }

  /**
   * Get or set pattern - fetch from cache or execute function and cache result
   */
  async getOrSet<T>(
    cacheType: CacheKey,
    identifier: string | number,
    fetchFunction: () => Promise<T>,
    params?: Record<string, any>,
    customTTL?: number
  ): Promise<T> {
    // Try to get from cache first
    const cached = this.get<T>(cacheType, identifier, params);
    if (cached !== undefined) {
      return cached;
    }

    // Not in cache, fetch the data
    Logger.debug('Cache miss - fetching data', {
      cacheType,
      identifier,
      params,
    });

    try {
      const data = await fetchFunction();

      // Cache the result
      this.set(cacheType, identifier, data, params, customTTL);

      return data;
    } catch (error) {
      Logger.error('Failed to fetch data for cache', error as Error, {
        cacheType,
        identifier,
        params,
      });
      throw error;
    }
  }

  /**
   * Clear all cache entries of a specific type
   */
  clearType(cacheType: CacheKey): void {
    const keys = this.cache.keys();
    const typeKeys = keys.filter(key => key.startsWith(`${cacheType}:`));

    if (typeKeys.length > 0) {
      this.cache.del(typeKeys);
      Logger.info('Cache type cleared', {
        cacheType,
        keysCleared: typeKeys.length,
      });
    }
  }

  /**
   * Clear all cache entries
   */
  clearAll(): void {
    this.cache.flushAll();
    this.hitCount = 0;
    this.missCount = 0;
    Logger.info('All cache cleared');
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    keys: number;
    hits: number;
    misses: number;
    hitRate: number;
    size: number;
  } {
    const keys = this.cache.keys().length;
    const total = this.hitCount + this.missCount;
    const hitRate = total > 0 ? (this.hitCount / total) * 100 : 0;

    return {
      keys,
      hits: this.hitCount,
      misses: this.missCount,
      hitRate: Math.round(hitRate * 100) / 100,
      size: this.cache.getStats().vsize || 0,
    };
  }

  /**
   * Get cache keys by pattern
   */
  getKeysByPattern(pattern: string): string[] {
    const keys = this.cache.keys();
    return keys.filter(key => key.includes(pattern));
  }

  /**
   * Get TTL for a specific key
   */
  getTTLForKey(cacheType: CacheKey, identifier: string | number, params?: Record<string, any>): number {
    const key = this.generateKey(cacheType, identifier, params);
    return this.cache.getTtl(key) || 0;
  }

  /**
   * Extend TTL for a specific key
   */
  extendTTL(
    cacheType: CacheKey,
    identifier: string | number,
    additionalSeconds: number,
    params?: Record<string, any>
  ): boolean {
    const key = this.generateKey(cacheType, identifier, params);
    const currentTTL = this.cache.getTtl(key);

    if (currentTTL && currentTTL > 0) {
      const newTTL = Math.floor((currentTTL - Date.now()) / 1000) + additionalSeconds;
      return this.cache.ttl(key, newTTL);
    }

    return false;
  }

  /**
   * Warm up cache with commonly accessed data
   */
  async warmUp(warmUpFunctions: Array<() => Promise<void>>): Promise<void> {
    Logger.info('Starting cache warm-up', {
      functions: warmUpFunctions.length,
    });

    const startTime = Date.now();

    try {
      await Promise.all(warmUpFunctions.map(fn => fn()));

      const duration = Date.now() - startTime;
      Logger.info('Cache warm-up completed', {
        duration,
        cacheStats: this.getStats(),
      });
    } catch (error) {
      Logger.error('Cache warm-up failed', error as Error);
      throw error;
    }
  }
}

// Export singleton instance
export const cacheService = new CacheService();
