"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportGenerator = void 0;
const date_utils_1 = require("@/utils/date-utils");
const logger_1 = require("@/utils/logger");
const settings_1 = require("@/config/settings");
const chalk_1 = __importDefault(require("chalk"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
class ReportGenerator {
    /**
     * Generate formatted report
     */
    static generateReport(report, format = 'console') {
        logger_1.Logger.info('Generating report', {
            format,
            opportunities: report.opportunities.length,
            targetDate: report.metadata.targetDate,
        });
        switch (format) {
            case 'console':
                return this.generateConsoleReport(report);
            case 'json':
                return this.generateJsonReport(report);
            case 'html':
                return this.generateHtmlReport(report);
            default:
                throw new Error(`Unsupported output format: ${format}`);
        }
    }
    /**
     * Save report to file
     */
    static async saveReport(report, format = 'console') {
        if (!settings_1.config.output.saveReports) {
            logger_1.Logger.debug('Report saving disabled');
            return '';
        }
        // Ensure reports directory exists
        const reportsDir = settings_1.config.output.reportsDir;
        if (!fs_1.default.existsSync(reportsDir)) {
            fs_1.default.mkdirSync(reportsDir, { recursive: true });
        }
        // Generate filename
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const extension = format === 'json' ? 'json' : format === 'html' ? 'html' : 'txt';
        const filename = `cs2-betting-analysis-${timestamp}.${extension}`;
        const filepath = path_1.default.join(reportsDir, filename);
        // Generate and save report
        const content = this.generateReport(report, format);
        fs_1.default.writeFileSync(filepath, content, 'utf8');
        logger_1.Logger.info('Report saved', {
            filepath,
            format,
            size: content.length,
        });
        return filepath;
    }
    /**
     * Generate console-formatted report
     */
    static generateConsoleReport(report) {
        const lines = [];
        // Header
        lines.push(chalk_1.default.bold.cyan('═'.repeat(80)));
        lines.push(chalk_1.default.bold.cyan('🎯 CS2 BETTING ANALYTICS - ULTIMATE ANALYSIS REPORT'));
        lines.push(chalk_1.default.bold.cyan('═'.repeat(80)));
        lines.push('');
        // Metadata
        lines.push(chalk_1.default.bold.yellow('📊 ANALYSIS METADATA'));
        lines.push(chalk_1.default.gray('─'.repeat(40)));
        lines.push(`📅 Target Date: ${chalk_1.default.white(report.metadata.targetDate)}`);
        lines.push(`🕐 Analysis Time: ${chalk_1.default.white(date_utils_1.DateUtils.formatForDisplay(report.metadata.analysisDate))}`);
        lines.push(`⚡ Execution Time: ${chalk_1.default.white(report.metadata.executionTime)}ms`);
        lines.push(`🎮 Total Matches: ${chalk_1.default.white(report.metadata.totalMatches)}`);
        lines.push(`✅ Qualifying Matches: ${chalk_1.default.white(report.metadata.qualifyingMatches)}`);
        lines.push(`📡 API Requests: ${chalk_1.default.white(report.metadata.apiRequestsUsed)}`);
        lines.push('');
        // Top 5 Opportunities
        if (report.opportunities.length > 0) {
            lines.push(chalk_1.default.bold.green('🏆 TOP 5 BETTING OPPORTUNITIES'));
            lines.push(chalk_1.default.gray('═'.repeat(80)));
            lines.push('');
            report.opportunities.slice(0, 5).forEach((opp, index) => {
                lines.push(...this.formatOpportunity(opp, index + 1));
                if (index < 4)
                    lines.push('');
            });
        }
        else {
            lines.push(chalk_1.default.bold.red('❌ NO BETTING OPPORTUNITIES FOUND'));
            lines.push(chalk_1.default.yellow('No positive EV opportunities identified for the target date.'));
            lines.push('');
        }
        // Summary Statistics
        lines.push(chalk_1.default.bold.blue('📈 SUMMARY STATISTICS'));
        lines.push(chalk_1.default.gray('─'.repeat(40)));
        lines.push(`🛡️  Low Risk Bets: ${chalk_1.default.green(report.summary.riskDistribution.low)}`);
        lines.push(`⚖️  Medium Risk Bets: ${chalk_1.default.yellow(report.summary.riskDistribution.medium)}`);
        lines.push(`🚫 High Risk Bets: ${chalk_1.default.red(report.summary.riskDistribution.high)}`);
        lines.push('');
        // Market Inefficiencies
        if (report.summary.marketInefficiencies.length > 0) {
            lines.push(chalk_1.default.bold.magenta('💎 MARKET INEFFICIENCIES DETECTED'));
            lines.push(chalk_1.default.gray('─'.repeat(40)));
            report.summary.marketInefficiencies.forEach(inefficiency => {
                lines.push(`• ${chalk_1.default.white(inefficiency)}`);
            });
            lines.push('');
        }
        // Data Quality Assessment
        lines.push(chalk_1.default.bold.cyan('🔍 DATA QUALITY ASSESSMENT'));
        lines.push(chalk_1.default.gray('─'.repeat(40)));
        lines.push(`📊 Overall Completeness: ${this.colorizePercentage(report.dataQuality.overallCompleteness)}%`);
        lines.push(`🎯 Reliability Score: ${this.colorizePercentage(report.dataQuality.reliabilityScore)}%`);
        if (report.dataQuality.missingDataPoints.length > 0) {
            lines.push(`⚠️  Missing Data Points:`);
            report.dataQuality.missingDataPoints.forEach(point => {
                lines.push(`   • ${chalk_1.default.yellow(point)}`);
            });
        }
        if (report.dataQuality.recommendations.length > 0) {
            lines.push(`💡 Recommendations:`);
            report.dataQuality.recommendations.forEach(rec => {
                lines.push(`   • ${chalk_1.default.cyan(rec)}`);
            });
        }
        lines.push('');
        // Warnings
        if (report.warnings.length > 0) {
            lines.push(chalk_1.default.bold.red('⚠️  WARNINGS'));
            lines.push(chalk_1.default.gray('─'.repeat(40)));
            report.warnings.forEach(warning => {
                lines.push(`• ${chalk_1.default.yellow(warning)}`);
            });
            lines.push('');
        }
        // Disclaimers
        lines.push(chalk_1.default.bold.gray('📋 DISCLAIMERS'));
        lines.push(chalk_1.default.gray('─'.repeat(40)));
        report.disclaimers.forEach(disclaimer => {
            lines.push(`• ${chalk_1.default.gray(disclaimer)}`);
        });
        lines.push('');
        // Footer
        lines.push(chalk_1.default.bold.cyan('═'.repeat(80)));
        lines.push(chalk_1.default.bold.cyan('🚀 END OF ANALYSIS - TRADE RESPONSIBLY'));
        lines.push(chalk_1.default.bold.cyan('═'.repeat(80)));
        return lines.join('\n');
    }
    /**
     * Format individual betting opportunity for console
     */
    static formatOpportunity(opp, rank) {
        const lines = [];
        // Header
        const riskEmoji = opp.risk.level === 'low' ? '🛡️' : opp.risk.level === 'medium' ? '⚖️' : '🚫';
        lines.push(chalk_1.default.bold.white(`RANK #${rank}: ${opp.match.team1} vs ${opp.match.team2} — Bet: ${opp.bet.type.replace('_', ' ').toUpperCase()}`));
        // Basic Info
        lines.push(`Tournament: ${chalk_1.default.cyan(opp.match.tournament)} (${opp.match.stage})`);
        lines.push(`Date/Time: ${chalk_1.default.white(date_utils_1.DateUtils.formatForUTC(opp.match.date))}`);
        lines.push(`Odds: ${chalk_1.default.white(opp.bet.odds)} (Source: ${opp.bet.oddsSource})`);
        lines.push(`Risk Level: ${riskEmoji} ${chalk_1.default.white(opp.risk.level.charAt(0).toUpperCase() + opp.risk.level.slice(1))}`);
        lines.push(`Confidence: ${this.colorizePercentage(opp.analysis.confidence)}%`);
        lines.push(`Expected Value: ${this.colorizeEV(opp.analysis.expectedValue)}%`);
        lines.push('');
        // Analysis Summary
        lines.push(chalk_1.default.bold.yellow('ANALYSIS SUMMARY:'));
        lines.push(`• Team Rankings: ${chalk_1.default.white(opp.analysis.summary.teamRankings)}`);
        lines.push(`• Recent Form: ${chalk_1.default.white(opp.analysis.summary.recentForm)}`);
        if (opp.analysis.summary.mapPoolAdvantage.length > 0) {
            lines.push(`• Map Pool Advantage:`);
            opp.analysis.summary.mapPoolAdvantage.forEach(advantage => {
                lines.push(`  ${chalk_1.default.white(advantage)}`);
            });
        }
        if (opp.analysis.summary.keyPlayerMatchups.length > 0) {
            lines.push(`• Key Player Matchups: ${chalk_1.default.white(opp.analysis.summary.keyPlayerMatchups.join(' vs '))}`);
        }
        if (opp.analysis.summary.advancedMetrics.length > 0) {
            lines.push(`• Advanced Metrics:`);
            opp.analysis.summary.advancedMetrics.forEach(metric => {
                lines.push(`  ${chalk_1.default.white(metric)}`);
            });
        }
        if (opp.analysis.summary.tournamentContext.length > 0) {
            lines.push(`• Tournament Context:`);
            opp.analysis.summary.tournamentContext.forEach(context => {
                lines.push(`  ${chalk_1.default.white(context)}`);
            });
        }
        lines.push('');
        // Justification
        lines.push(chalk_1.default.bold.green('JUSTIFICATION:'));
        lines.push(chalk_1.default.white(opp.analysis.justification));
        lines.push('');
        // Red Flags
        if (opp.analysis.redFlags.length > 0) {
            lines.push(chalk_1.default.bold.red('RED FLAGS:'));
            opp.analysis.redFlags.forEach(flag => {
                lines.push(`• ${chalk_1.default.red(flag)}`);
            });
            lines.push('');
        }
        return lines;
    }
    /**
     * Generate JSON report
     */
    static generateJsonReport(report) {
        return JSON.stringify(report, null, 2);
    }
    /**
     * Generate HTML report
     */
    static generateHtmlReport(report) {
        const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CS2 Betting Analytics Report - ${report.metadata.targetDate}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #1a1a1a; color: #ffffff; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; }
        .header h1 { margin: 0; font-size: 2.5em; text-shadow: 2px 2px 4px rgba(0,0,0,0.5); }
        .metadata { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .metadata-item { background: #2a2a2a; padding: 15px; border-radius: 8px; border-left: 4px solid #667eea; }
        .opportunity { background: #2a2a2a; margin-bottom: 20px; padding: 20px; border-radius: 10px; border: 1px solid #444; }
        .opportunity-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .rank { background: #667eea; color: white; padding: 5px 15px; border-radius: 20px; font-weight: bold; }
        .risk-low { color: #4CAF50; }
        .risk-medium { color: #FF9800; }
        .risk-high { color: #F44336; }
        .ev-positive { color: #4CAF50; font-weight: bold; }
        .ev-negative { color: #F44336; font-weight: bold; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #2a2a2a; padding: 20px; border-radius: 10px; border-top: 4px solid #667eea; }
        .footer { text-align: center; margin-top: 40px; padding: 20px; background: #2a2a2a; border-radius: 10px; }
        .disclaimer { font-size: 0.9em; color: #aaa; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 CS2 Betting Analytics Report</h1>
            <p>Ultimate Analysis for ${report.metadata.targetDate}</p>
        </div>
        
        <div class="metadata">
            <div class="metadata-item">
                <strong>Analysis Date:</strong><br>
                ${date_utils_1.DateUtils.formatForDisplay(report.metadata.analysisDate)}
            </div>
            <div class="metadata-item">
                <strong>Total Matches:</strong><br>
                ${report.metadata.totalMatches}
            </div>
            <div class="metadata-item">
                <strong>Qualifying Matches:</strong><br>
                ${report.metadata.qualifyingMatches}
            </div>
            <div class="metadata-item">
                <strong>Execution Time:</strong><br>
                ${report.metadata.executionTime}ms
            </div>
        </div>
        
        ${report.opportunities.length > 0 ? `
        <h2>🏆 Top Betting Opportunities</h2>
        ${report.opportunities.slice(0, 5).map(opp => `
        <div class="opportunity">
            <div class="opportunity-header">
                <div class="rank">RANK #${opp.rank}</div>
                <div class="risk-${opp.risk.level}">Risk: ${opp.risk.level.toUpperCase()}</div>
            </div>
            <h3>${opp.match.team1} vs ${opp.match.team2}</h3>
            <p><strong>Bet:</strong> ${opp.bet.selection}</p>
            <p><strong>Odds:</strong> ${opp.bet.odds} | <strong>Expected Value:</strong> <span class="ev-positive">+${opp.analysis.expectedValue.toFixed(2)}%</span></p>
            <p><strong>Confidence:</strong> ${opp.analysis.confidence.toFixed(1)}%</p>
            <p><strong>Tournament:</strong> ${opp.match.tournament} (${opp.match.stage})</p>
            <p><strong>Justification:</strong> ${opp.analysis.justification}</p>
        </div>
        `).join('')}
        ` : '<h2>❌ No Betting Opportunities Found</h2>'}
        
        <div class="summary-grid">
            <div class="summary-card">
                <h3>📊 Risk Distribution</h3>
                <p>🛡️ Low Risk: ${report.summary.riskDistribution.low}</p>
                <p>⚖️ Medium Risk: ${report.summary.riskDistribution.medium}</p>
                <p>🚫 High Risk: ${report.summary.riskDistribution.high}</p>
            </div>
            <div class="summary-card">
                <h3>🔍 Data Quality</h3>
                <p>Completeness: ${report.dataQuality.overallCompleteness.toFixed(1)}%</p>
                <p>Reliability: ${report.dataQuality.reliabilityScore.toFixed(1)}%</p>
            </div>
        </div>
        
        <div class="footer">
            ${report.disclaimers.map(disclaimer => `<div class="disclaimer">${disclaimer}</div>`).join('')}
        </div>
    </div>
</body>
</html>`;
        return html;
    }
    /**
     * Colorize percentage values for console output
     */
    static colorizePercentage(value) {
        if (value >= 80)
            return chalk_1.default.green(value.toFixed(1));
        if (value >= 60)
            return chalk_1.default.yellow(value.toFixed(1));
        return chalk_1.default.red(value.toFixed(1));
    }
    /**
     * Colorize expected value for console output
     */
    static colorizeEV(value) {
        if (value > 0)
            return chalk_1.default.green(`+${value.toFixed(2)}`);
        return chalk_1.default.red(value.toFixed(2));
    }
    /**
     * Display report to console
     */
    static displayReport(report) {
        const consoleReport = this.generateConsoleReport(report);
        console.log(consoleReport);
    }
}
exports.ReportGenerator = ReportGenerator;
//# sourceMappingURL=report-generator.js.map