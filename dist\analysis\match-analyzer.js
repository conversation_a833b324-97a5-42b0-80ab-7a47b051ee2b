"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MatchAnalyzer = void 0;
const data_fetcher_1 = require("@/api/data-fetcher");
const team_analyzer_1 = require("./team-analyzer");
const risk_assessor_1 = require("./risk-assessor");
const ev_calculator_1 = require("./ev-calculator");
const logger_1 = require("@/utils/logger");
const date_utils_1 = require("@/utils/date-utils");
const math_utils_1 = require("@/utils/math-utils");
const validation_1 = require("@/utils/validation");
class MatchAnalyzer {
    constructor() {
        this.dataFetcher = new data_fetcher_1.DataFetcher();
    }
    /**
     * Analyze all matches for a specific date
     */
    async analyzeMatchesForDate(date) {
        const startTime = Date.now();
        const dateStr = date_utils_1.DateUtils.formatForDisplay(date);
        logger_1.Logger.analysisStart(dateStr, 'date_analysis');
        try {
            // Fetch all matches for the date
            const matches = await this.dataFetcher.fetchMatches(date);
            logger_1.Logger.info('Matches found for analysis', {
                date: dateStr,
                count: matches.length,
            });
            if (matches.length === 0) {
                return this.createEmptyReport(date, startTime);
            }
            // Analyze each match
            const matchAnalyses = [];
            const opportunities = [];
            for (const match of matches) {
                try {
                    const analysis = await this.analyzeMatch(match.id);
                    matchAnalyses.push(analysis);
                    // Convert analysis to betting opportunities
                    const matchOpportunities = this.generateBettingOpportunities(analysis);
                    opportunities.push(...matchOpportunities);
                }
                catch (error) {
                    logger_1.Logger.error(`Failed to analyze match ${match.id}`, error, {
                        matchId: match.id,
                        teams: `${match.team1?.name} vs ${match.team2?.name}`,
                    });
                }
            }
            // Filter and rank opportunities
            const validOpportunities = this.filterAndRankOpportunities(opportunities);
            // Generate final report
            const report = this.generateAnalysisReport(date, matches, matchAnalyses, validOpportunities, startTime);
            const duration = Date.now() - startTime;
            logger_1.Logger.analysisComplete(dateStr, duration, matches.length, validOpportunities.length);
            return report;
        }
        catch (error) {
            logger_1.Logger.error('Date analysis failed', error, { date: dateStr });
            throw error;
        }
    }
    /**
     * Analyze a single match
     */
    async analyzeMatch(matchId) {
        logger_1.Logger.debug('Analyzing match', { matchId });
        const startTime = Date.now();
        try {
            // Fetch comprehensive match data
            const matchData = await this.dataFetcher.fetchMatchAnalysisData(matchId);
            // Validate match data
            const matchValidation = validation_1.ValidationUtils.validateMatch(matchData.match);
            if (!matchValidation.isValid) {
                throw new Error(`Invalid match data: ${matchValidation.errors.join(', ')}`);
            }
            // Analyze both teams
            const team1Analysis = team_analyzer_1.TeamAnalyzer.analyzeTeam(matchData.team1Data.info, matchData.team1Data.stats, matchData.rankings);
            const team2Analysis = team_analyzer_1.TeamAnalyzer.analyzeTeam(matchData.team2Data.info, matchData.team2Data.stats, matchData.rankings);
            // Assess risk
            const riskAssessment = risk_assessor_1.RiskAssessor.assessRisk({
                team1Analysis,
                team2Analysis,
                matchFormat: matchData.match.format?.type || 'bo3',
                tournamentTier: matchData.match.event?.tier || 'B',
                significance: matchData.match.significance || 3,
            });
            // Calculate expected values for different bet types
            const evCalculations = [];
            // Only calculate EV if match meets minimum criteria
            if (risk_assessor_1.RiskAssessor.meetsMinimumCriteria({
                team1Analysis,
                team2Analysis,
                matchFormat: matchData.match.format?.type || 'bo3',
                tournamentTier: matchData.match.event?.tier || 'B',
                significance: matchData.match.significance || 3,
            })) {
                // Determine favored team
                const favoredTeam = team1Analysis.ranking <= team2Analysis.ranking ? 'team1' : 'team2';
                // Mock odds for demonstration (in real implementation, fetch from betting APIs)
                const mockOdds = this.generateMockOdds(team1Analysis, team2Analysis);
                const evContext = {
                    team1Analysis,
                    team2Analysis,
                    riskAssessment,
                    matchFormat: matchData.match.format?.type || 'bo3',
                    significance: matchData.match.significance || 3,
                    odds: mockOdds,
                };
                // Calculate match winner EV
                try {
                    const matchWinnerEV = ev_calculator_1.EVCalculator.calculateMatchWinnerEV(evContext, favoredTeam);
                    evCalculations.push(matchWinnerEV);
                }
                catch (error) {
                    logger_1.Logger.warn('Failed to calculate match winner EV', { matchId, error: error.message });
                }
                // Calculate map handicap EV if applicable
                if (matchData.match.format?.type === 'bo3') {
                    try {
                        const handicapEV = ev_calculator_1.EVCalculator.calculateMapHandicapEV(evContext, -1.5, mockOdds.team1 > mockOdds.team2 ? 1.8 : 2.1, favoredTeam);
                        evCalculations.push(handicapEV);
                    }
                    catch (error) {
                        logger_1.Logger.warn('Failed to calculate handicap EV', { matchId, error: error.message });
                    }
                }
            }
            const duration = Date.now() - startTime;
            logger_1.Logger.performance('match_analysis', duration, {
                matchId,
                team1: team1Analysis.name,
                team2: team2Analysis.name,
                riskLevel: riskAssessment.riskLevel,
                evOpportunities: evCalculations.length,
            });
            return {
                match: matchData.match,
                team1Analysis,
                team2Analysis,
                riskAssessment,
                evCalculations,
            };
        }
        catch (error) {
            logger_1.Logger.error('Match analysis failed', error, { matchId });
            throw error;
        }
    }
    /**
     * Generate betting opportunities from match analysis
     */
    generateBettingOpportunities(analysis) {
        const opportunities = [];
        for (const evCalc of analysis.evCalculations) {
            // Only include positive EV opportunities
            if (!evCalc.expectedValue.isPositive)
                continue;
            // Only include if recommendation is to bet
            if (evCalc.recommendation.action !== 'bet')
                continue;
            const opportunity = {
                rank: 0, // Will be set during ranking
                match: {
                    id: analysis.match.id,
                    team1: analysis.team1Analysis.name,
                    team2: analysis.team2Analysis.name,
                    date: new Date(analysis.match.date),
                    tournament: analysis.match.event?.name || 'Unknown',
                    stage: analysis.match.event?.stage || 'Unknown',
                },
                bet: {
                    type: evCalc.betType,
                    selection: evCalc.selection,
                    odds: evCalc.odds.decimal,
                    oddsSource: evCalc.odds.source,
                    stake: evCalc.kelly.recommendedStake,
                },
                risk: {
                    level: analysis.riskAssessment.riskLevel,
                    factors: analysis.riskAssessment.riskFactors,
                    mitigatingFactors: analysis.riskAssessment.mitigatingFactors,
                },
                analysis: {
                    confidence: evCalc.assessment.confidence,
                    expectedValue: evCalc.expectedValue.percentage,
                    impliedProbability: evCalc.odds.impliedProbability,
                    assessedProbability: evCalc.assessment.trueProbability,
                    summary: {
                        teamRankings: `${analysis.team1Analysis.name}: #${analysis.team1Analysis.ranking} vs ${analysis.team2Analysis.name}: #${analysis.team2Analysis.ranking}`,
                        recentForm: `${analysis.team1Analysis.name}: ${analysis.team1Analysis.form.recent.winRate}% vs ${analysis.team2Analysis.name}: ${analysis.team2Analysis.form.recent.winRate}%`,
                        mapPoolAdvantage: this.summarizeMapPoolAdvantage(analysis),
                        keyPlayerMatchups: this.summarizeKeyPlayerMatchups(analysis),
                        advancedMetrics: this.summarizeAdvancedMetrics(analysis),
                        tournamentContext: this.summarizeTournamentContext(analysis),
                    },
                    justification: evCalc.recommendation.reasoning,
                    redFlags: analysis.riskAssessment.exclusionFactors.map(f => f.reason),
                },
                dataQuality: {
                    completeness: Math.min(analysis.team1Analysis.dataQuality.completeness, analysis.team2Analysis.dataQuality.completeness),
                    reliability: analysis.team1Analysis.dataQuality.reliability === 'high' &&
                        analysis.team2Analysis.dataQuality.reliability === 'high' ? 'high' :
                        analysis.team1Analysis.dataQuality.reliability === 'low' ||
                            analysis.team2Analysis.dataQuality.reliability === 'low' ? 'low' : 'medium',
                    lastUpdated: new Date(),
                    sources: ['HLTV'],
                },
            };
            opportunities.push(opportunity);
        }
        return opportunities;
    }
    /**
     * Filter and rank betting opportunities
     */
    filterAndRankOpportunities(opportunities) {
        // Filter out high-risk opportunities
        const filtered = opportunities.filter(opp => opp.risk.level !== 'high');
        // Sort by expected value and confidence
        const ranked = filtered.sort((a, b) => {
            // Primary sort: Expected value
            const evDiff = b.analysis.expectedValue - a.analysis.expectedValue;
            if (Math.abs(evDiff) > 1)
                return evDiff;
            // Secondary sort: Confidence
            const confDiff = b.analysis.confidence - a.analysis.confidence;
            if (Math.abs(confDiff) > 5)
                return confDiff;
            // Tertiary sort: Risk level (low risk preferred)
            const riskScore = (risk) => risk === 'low' ? 2 : risk === 'medium' ? 1 : 0;
            return riskScore(b.risk.level) - riskScore(a.risk.level);
        });
        // Assign ranks and return top 5
        return ranked.slice(0, 5).map((opp, index) => ({
            ...opp,
            rank: index + 1,
        }));
    }
    /**
     * Generate mock odds for demonstration
     */
    generateMockOdds(team1, team2) {
        // Simple odds generation based on ranking difference
        const rankingDiff = team2.ranking - team1.ranking;
        const team1Probability = 50 + Math.min(rankingDiff * 2, 30);
        return {
            team1: math_utils_1.MathUtils.round(100 / team1Probability, 2),
            team2: math_utils_1.MathUtils.round(100 / (100 - team1Probability), 2),
            source: 'Mock Odds',
        };
    }
    /**
     * Create empty report when no matches found
     */
    createEmptyReport(date, startTime) {
        return {
            metadata: {
                analysisDate: new Date(),
                targetDate: date_utils_1.DateUtils.formatForDisplay(date),
                totalMatches: 0,
                qualifyingMatches: 0,
                executionTime: Date.now() - startTime,
                apiRequestsUsed: this.dataFetcher.getClientStats().requests,
            },
            opportunities: [],
            summary: {
                topRecommendations: [],
                riskDistribution: { low: 0, medium: 0, high: 0 },
                alternativeOptions: [],
                marketInefficiencies: [],
            },
            dataQuality: {
                overallCompleteness: 0,
                reliabilityScore: 0,
                missingDataPoints: ['No matches found for the specified date'],
                recommendations: ['Try analyzing a different date with scheduled matches'],
            },
            warnings: ['No matches found for the specified date'],
            disclaimers: [
                'This analysis is for educational purposes only',
                'Past performance does not guarantee future results',
                'Always gamble responsibly',
            ],
        };
    }
    // Helper methods for summarizing analysis data
    summarizeMapPoolAdvantage(analysis) {
        const team1Strong = analysis.team1Analysis.mapPool.strongMaps.map(m => m.map);
        const team2Strong = analysis.team2Analysis.mapPool.strongMaps.map(m => m.map);
        return [
            `${analysis.team1Analysis.name} strong on: ${team1Strong.join(', ') || 'None'}`,
            `${analysis.team2Analysis.name} strong on: ${team2Strong.join(', ') || 'None'}`,
        ];
    }
    summarizeKeyPlayerMatchups(analysis) {
        const team1Key = analysis.team1Analysis.roster.keyPlayers[0];
        const team2Key = analysis.team2Analysis.roster.keyPlayers[0];
        return [
            team1Key ? `${team1Key.name} (${team1Key.rating})` : 'No key player data',
            team2Key ? `${team2Key.name} (${team2Key.rating})` : 'No key player data',
        ];
    }
    summarizeAdvancedMetrics(analysis) {
        return [
            `Opening kills: ${analysis.team1Analysis.performance.openingKills}% vs ${analysis.team2Analysis.performance.openingKills}%`,
            `Clutch performance: ${analysis.team1Analysis.performance.clutchPerformance}% vs ${analysis.team2Analysis.performance.clutchPerformance}%`,
        ];
    }
    summarizeTournamentContext(analysis) {
        return [
            `Tournament: ${analysis.match.event?.name || 'Unknown'}`,
            `Format: ${analysis.match.format?.type || 'Unknown'}`,
            `Significance: ${analysis.match.significance || 'Unknown'}/5`,
        ];
    }
    /**
     * Generate comprehensive analysis report
     */
    generateAnalysisReport(date, matches, analyses, opportunities, startTime) {
        const riskDistribution = {
            low: opportunities.filter(o => o.risk.level === 'low').length,
            medium: opportunities.filter(o => o.risk.level === 'medium').length,
            high: opportunities.filter(o => o.risk.level === 'high').length,
        };
        const avgCompleteness = analyses.length > 0
            ? analyses.reduce((sum, a) => sum + Math.min(a.team1Analysis.dataQuality.completeness, a.team2Analysis.dataQuality.completeness), 0) / analyses.length
            : 0;
        return {
            metadata: {
                analysisDate: new Date(),
                targetDate: date_utils_1.DateUtils.formatForDisplay(date),
                totalMatches: matches.length,
                qualifyingMatches: analyses.length,
                executionTime: Date.now() - startTime,
                apiRequestsUsed: this.dataFetcher.getClientStats().requests,
            },
            opportunities,
            summary: {
                topRecommendations: opportunities.slice(0, 5),
                riskDistribution,
                alternativeOptions: opportunities.slice(5, 10),
                marketInefficiencies: this.identifyMarketInefficiencies(opportunities),
            },
            dataQuality: {
                overallCompleteness: math_utils_1.MathUtils.round(avgCompleteness, 1),
                reliabilityScore: math_utils_1.MathUtils.round(avgCompleteness * 0.8, 1), // Simplified reliability score
                missingDataPoints: this.identifyMissingDataPoints(analyses),
                recommendations: this.generateDataRecommendations(analyses),
            },
            warnings: this.generateWarnings(opportunities, analyses),
            disclaimers: [
                'This analysis is for educational purposes only',
                'Past performance does not guarantee future results',
                'Always gamble responsibly and within your means',
                'Odds and probabilities are estimates based on available data',
            ],
        };
    }
    identifyMarketInefficiencies(opportunities) {
        return opportunities
            .filter(o => o.analysis.expectedValue > 5)
            .map(o => `${o.match.team1} vs ${o.match.team2}: ${o.analysis.expectedValue.toFixed(1)}% EV`);
    }
    identifyMissingDataPoints(analyses) {
        const missing = [];
        analyses.forEach(analysis => {
            if (analysis.team1Analysis.dataQuality.completeness < 70) {
                missing.push(`Incomplete data for ${analysis.team1Analysis.name}`);
            }
            if (analysis.team2Analysis.dataQuality.completeness < 70) {
                missing.push(`Incomplete data for ${analysis.team2Analysis.name}`);
            }
        });
        return [...new Set(missing)]; // Remove duplicates
    }
    generateDataRecommendations(analyses) {
        const recommendations = [];
        if (analyses.some(a => a.team1Analysis.dataQuality.sampleSize < 10 || a.team2Analysis.dataQuality.sampleSize < 10)) {
            recommendations.push('Some teams have limited recent match data - consider waiting for more games');
        }
        if (analyses.some(a => a.riskAssessment.exclusionFactors.length > 0)) {
            recommendations.push('Several matches have risk factors - review exclusion criteria carefully');
        }
        return recommendations;
    }
    generateWarnings(opportunities, analyses) {
        const warnings = [];
        if (opportunities.length === 0) {
            warnings.push('No positive EV opportunities found for this date');
        }
        if (opportunities.some(o => o.risk.level === 'medium')) {
            warnings.push('Some opportunities carry medium risk - proceed with caution');
        }
        if (analyses.some(a => a.team1Analysis.dataQuality.reliability === 'low' || a.team2Analysis.dataQuality.reliability === 'low')) {
            warnings.push('Some analyses based on limited data - results may be less reliable');
        }
        return warnings;
    }
}
exports.MatchAnalyzer = MatchAnalyzer;
//# sourceMappingURL=match-analyzer.js.map