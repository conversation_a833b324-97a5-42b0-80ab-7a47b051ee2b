"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EVCalculator = void 0;
const math_utils_1 = require("@/utils/math-utils");
const logger_1 = require("@/utils/logger");
const settings_1 = require("@/config/settings");
class EVCalculator {
    /**
     * Calculate expected value for match winner bet
     */
    static calculateMatchWinnerEV(context, favoredTeam) {
        logger_1.Logger.debug('Calculating match winner EV', {
            team1: context.team1Analysis.name,
            team2: context.team2Analysis.name,
            favoredTeam,
        });
        const favoredAnalysis = favoredTeam === 'team1' ? context.team1Analysis : context.team2Analysis;
        const underdogAnalysis = favoredTeam === 'team1' ? context.team2Analysis : context.team1Analysis;
        const odds = favoredTeam === 'team1' ? context.odds?.team1 : context.odds?.team2;
        if (!odds) {
            throw new Error('Odds not available for EV calculation');
        }
        // Calculate true probability
        const trueProbability = this.calculateMatchWinProbability(favoredAnalysis, underdogAnalysis, context);
        // Calculate confidence in assessment
        const confidence = this.calculateConfidence(context, 'match_winner');
        // Calculate expected value
        const impliedProbability = math_utils_1.MathUtils.impliedProbability(odds);
        const expectedValue = math_utils_1.MathUtils.expectedValue(trueProbability, odds);
        const expectedValuePercentage = math_utils_1.MathUtils.expectedValuePercentage(trueProbability, odds);
        // Calculate Kelly Criterion
        const kellyFraction = math_utils_1.MathUtils.kellyCriterion(trueProbability, odds, settings_1.config.analysis.defaultStakeAmount);
        // Generate factors and recommendation
        const factors = this.generateMatchWinnerFactors(favoredAnalysis, underdogAnalysis, context);
        const recommendation = this.generateRecommendation(expectedValuePercentage, confidence, context.riskAssessment);
        return {
            betType: 'match_winner',
            selection: `${favoredAnalysis.name} to win`,
            odds: {
                decimal: odds,
                impliedProbability: math_utils_1.MathUtils.round(impliedProbability, 2),
                source: context.odds?.source || 'Unknown',
            },
            assessment: {
                trueProbability: math_utils_1.MathUtils.round(trueProbability, 2),
                confidence: math_utils_1.MathUtils.round(confidence, 1),
                methodology: [
                    'Team form analysis',
                    'Head-to-head comparison',
                    'Map pool evaluation',
                    'Recent performance trends',
                    'Contextual factors',
                ],
            },
            expectedValue: {
                absolute: math_utils_1.MathUtils.round(expectedValue, 2),
                percentage: math_utils_1.MathUtils.round(expectedValuePercentage, 2),
                isPositive: expectedValue > 0,
            },
            kelly: {
                fraction: math_utils_1.MathUtils.round(kellyFraction / settings_1.config.analysis.defaultStakeAmount, 4),
                recommendedStake: math_utils_1.MathUtils.round(kellyFraction * 0.25, 2), // Conservative 25% of Kelly
                maxStake: math_utils_1.MathUtils.round(kellyFraction, 2),
            },
            factors,
            recommendation,
        };
    }
    /**
     * Calculate match win probability
     */
    static calculateMatchWinProbability(favoredTeam, underdogTeam, context) {
        // Base probability from ranking difference
        const rankingDiff = Math.abs(favoredTeam.ranking - underdogTeam.ranking);
        let baseProbability = 50 + Math.min(rankingDiff * 2, 30); // Max 80% from ranking alone
        // Adjust for recent form
        const formDifference = favoredTeam.form.recent.winRate - underdogTeam.form.recent.winRate;
        baseProbability += formDifference * 0.2; // Form impact
        // Adjust for overall rating difference
        const ratingDifference = favoredTeam.overallRating - underdogTeam.overallRating;
        baseProbability += ratingDifference * 10; // Rating impact
        // Map pool advantage
        const mapPoolAdvantage = favoredTeam.mapPool.overallAdvantage - underdogTeam.mapPool.overallAdvantage;
        baseProbability += mapPoolAdvantage * 0.3;
        // Format adjustment
        if (context.matchFormat.toLowerCase() === 'bo1') {
            baseProbability -= 5; // BO1 reduces favorite advantage
        }
        else if (context.matchFormat.toLowerCase() === 'bo5') {
            baseProbability += 3; // BO5 increases favorite advantage
        }
        // Consistency factor
        const consistencyDiff = favoredTeam.form.recent.consistency - underdogTeam.form.recent.consistency;
        baseProbability += consistencyDiff * 0.1;
        // Roster stability impact
        if (favoredTeam.roster.stability < 70) {
            baseProbability -= 3;
        }
        // Trend adjustment
        if (favoredTeam.form.recent.trend === 'improving')
            baseProbability += 2;
        if (favoredTeam.form.recent.trend === 'declining')
            baseProbability -= 3;
        if (underdogTeam.form.recent.trend === 'improving')
            baseProbability -= 2;
        if (underdogTeam.form.recent.trend === 'declining')
            baseProbability += 2;
        // Clamp to reasonable bounds
        return math_utils_1.MathUtils.clamp(baseProbability, 55, 85);
    }
    /**
     * Calculate map handicap EV
     */
    static calculateMapHandicapEV(context, line, odds, favoredTeam) {
        const favoredAnalysis = favoredTeam === 'team1' ? context.team1Analysis : context.team2Analysis;
        const underdogAnalysis = favoredTeam === 'team1' ? context.team2Analysis : context.team1Analysis;
        // Calculate probability of covering handicap
        const trueProbability = this.calculateHandicapProbability(favoredAnalysis, underdogAnalysis, line, context);
        const confidence = this.calculateConfidence(context, 'map_handicap');
        const impliedProbability = math_utils_1.MathUtils.impliedProbability(odds);
        const expectedValue = math_utils_1.MathUtils.expectedValue(trueProbability, odds);
        const expectedValuePercentage = math_utils_1.MathUtils.expectedValuePercentage(trueProbability, odds);
        const kellyFraction = math_utils_1.MathUtils.kellyCriterion(trueProbability, odds, settings_1.config.analysis.defaultStakeAmount);
        const factors = this.generateHandicapFactors(favoredAnalysis, underdogAnalysis, line, context);
        const recommendation = this.generateRecommendation(expectedValuePercentage, confidence, context.riskAssessment);
        return {
            betType: 'map_handicap',
            selection: `${favoredAnalysis.name} ${line > 0 ? '+' : ''}${line} maps`,
            odds: {
                decimal: odds,
                impliedProbability: math_utils_1.MathUtils.round(impliedProbability, 2),
                source: context.odds?.source || 'Unknown',
            },
            assessment: {
                trueProbability: math_utils_1.MathUtils.round(trueProbability, 2),
                confidence: math_utils_1.MathUtils.round(confidence, 1),
                methodology: [
                    'Map pool analysis',
                    'Historical map performance',
                    'Format-specific trends',
                    'Team consistency evaluation',
                ],
            },
            expectedValue: {
                absolute: math_utils_1.MathUtils.round(expectedValue, 2),
                percentage: math_utils_1.MathUtils.round(expectedValuePercentage, 2),
                isPositive: expectedValue > 0,
            },
            kelly: {
                fraction: math_utils_1.MathUtils.round(kellyFraction / settings_1.config.analysis.defaultStakeAmount, 4),
                recommendedStake: math_utils_1.MathUtils.round(kellyFraction * 0.25, 2),
                maxStake: math_utils_1.MathUtils.round(kellyFraction, 2),
            },
            factors,
            recommendation,
        };
    }
    /**
     * Calculate handicap covering probability
     */
    static calculateHandicapProbability(favoredTeam, underdogTeam, line, context) {
        // Base probability from team strength difference
        const strengthDiff = favoredTeam.overallRating - underdogTeam.overallRating;
        let baseProbability = 50 + (strengthDiff * 15);
        // Adjust for map pool advantages
        const mapPoolDiff = favoredTeam.mapPool.overallAdvantage - underdogTeam.mapPool.overallAdvantage;
        baseProbability += mapPoolDiff * 0.5;
        // Format-specific adjustments
        if (context.matchFormat.toLowerCase() === 'bo3') {
            if (Math.abs(line) === 1.5) {
                baseProbability += line > 0 ? 10 : -10; // Adjust for 2-0 vs 2-1 probability
            }
        }
        // Consistency impact on handicap
        const consistencyDiff = favoredTeam.form.recent.consistency - underdogTeam.form.recent.consistency;
        baseProbability += consistencyDiff * 0.15;
        return math_utils_1.MathUtils.clamp(baseProbability, 30, 80);
    }
    /**
     * Calculate confidence in EV assessment
     */
    static calculateConfidence(context, betType) {
        let baseConfidence = settings_1.config.analysis.minConfidenceThreshold;
        // Data quality impact
        const avgDataQuality = (context.team1Analysis.dataQuality.completeness +
            context.team2Analysis.dataQuality.completeness) / 2;
        baseConfidence += (avgDataQuality - 50) * 0.3;
        // Sample size impact
        const avgSampleSize = (context.team1Analysis.dataQuality.sampleSize +
            context.team2Analysis.dataQuality.sampleSize) / 2;
        baseConfidence += Math.min(avgSampleSize / 20, 1) * 10;
        // Risk assessment confidence
        baseConfidence += context.riskAssessment.confidence * 0.2;
        // Bet type specific adjustments
        if (betType === 'match_winner') {
            baseConfidence += 5; // More confident in match winner predictions
        }
        return math_utils_1.MathUtils.clamp(baseConfidence, settings_1.config.analysis.minConfidenceThreshold, settings_1.config.analysis.maxConfidenceThreshold);
    }
    /**
     * Generate factors for match winner bet
     */
    static generateMatchWinnerFactors(favoredTeam, underdogTeam, context) {
        const strengths = [];
        const concerns = [];
        const keyAssumptions = [];
        // Strengths
        if (favoredTeam.form.recent.winRate >= 70) {
            strengths.push('Favored team in excellent form');
        }
        if (favoredTeam.mapPool.strongMaps.length >= 3) {
            strengths.push('Strong map pool depth');
        }
        if (context.riskAssessment.riskLevel === 'low') {
            strengths.push('Low risk assessment');
        }
        // Concerns
        if (underdogTeam.form.recent.trend === 'improving') {
            concerns.push('Underdog showing improvement');
        }
        if (context.matchFormat.toLowerCase() === 'bo1') {
            concerns.push('BO1 format increases variance');
        }
        if (favoredTeam.roster.stability < 80) {
            concerns.push('Recent roster changes');
        }
        // Key assumptions
        keyAssumptions.push('Current form continues');
        keyAssumptions.push('No unexpected roster changes');
        keyAssumptions.push('Standard map veto process');
        return { strengths, concerns, keyAssumptions };
    }
    /**
     * Generate factors for handicap bet
     */
    static generateHandicapFactors(favoredTeam, underdogTeam, line, context) {
        const strengths = [];
        const concerns = [];
        const keyAssumptions = [];
        if (favoredTeam.mapPool.strongMaps.length >= Math.abs(line) + 1) {
            strengths.push('Sufficient map pool depth for handicap');
        }
        if (favoredTeam.form.recent.consistency >= 70) {
            strengths.push('Consistent recent performance');
        }
        if (underdogTeam.mapPool.weakMaps.length >= 2) {
            concerns.push('Underdog has exploitable map weaknesses');
        }
        keyAssumptions.push('Map veto follows expected pattern');
        keyAssumptions.push('No significant tactical surprises');
        return { strengths, concerns, keyAssumptions };
    }
    /**
     * Generate betting recommendation
     */
    static generateRecommendation(expectedValuePercentage, confidence, riskAssessment) {
        if (riskAssessment.recommendation === 'avoid') {
            return {
                action: 'pass',
                reasoning: 'Risk assessment recommends avoiding this bet',
                confidence: confidence,
            };
        }
        if (expectedValuePercentage < settings_1.config.analysis.minPositiveEvThreshold * 100) {
            return {
                action: 'pass',
                reasoning: 'Expected value below minimum threshold',
                confidence: confidence,
            };
        }
        if (expectedValuePercentage >= 5 && confidence >= 75 && riskAssessment.riskLevel === 'low') {
            return {
                action: 'bet',
                reasoning: 'Strong positive EV with high confidence and low risk',
                confidence: confidence,
            };
        }
        if (expectedValuePercentage >= 3 && confidence >= 70) {
            return {
                action: 'bet',
                reasoning: 'Positive EV with adequate confidence',
                confidence: confidence,
            };
        }
        return {
            action: 'monitor',
            reasoning: 'Marginal opportunity - monitor for better odds',
            confidence: confidence,
        };
    }
}
exports.EVCalculator = EVCalculator;
//# sourceMappingURL=ev-calculator.js.map