import { AnalysisReport, BettingOpportunity } from '@/models/betting';
import { DateUtils } from '@/utils/date-utils';
import { MathUtils } from '@/utils/math-utils';
import { Logger } from '@/utils/logger';
import { config } from '@/config/settings';
import chalk from 'chalk';
import fs from 'fs';
import path from 'path';

export type OutputFormat = 'console' | 'json' | 'html';

export class ReportGenerator {
  /**
   * Generate formatted report
   */
  static generateReport(report: AnalysisReport, format: OutputFormat = 'console'): string {
    Logger.info('Generating report', {
      format,
      opportunities: report.opportunities.length,
      targetDate: report.metadata.targetDate,
    });
    
    switch (format) {
      case 'console':
        return this.generateConsoleReport(report);
      case 'json':
        return this.generateJsonReport(report);
      case 'html':
        return this.generateHtmlReport(report);
      default:
        throw new Error(`Unsupported output format: ${format}`);
    }
  }
  
  /**
   * Save report to file
   */
  static async saveReport(report: AnalysisReport, format: OutputFormat = 'console'): Promise<string> {
    if (!config.output.saveReports) {
      Logger.debug('Report saving disabled');
      return '';
    }
    
    // Ensure reports directory exists
    const reportsDir = config.output.reportsDir;
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    // Generate filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const extension = format === 'json' ? 'json' : format === 'html' ? 'html' : 'txt';
    const filename = `cs2-betting-analysis-${timestamp}.${extension}`;
    const filepath = path.join(reportsDir, filename);
    
    // Generate and save report
    const content = this.generateReport(report, format);
    fs.writeFileSync(filepath, content, 'utf8');
    
    Logger.info('Report saved', {
      filepath,
      format,
      size: content.length,
    });
    
    return filepath;
  }
  
  /**
   * Generate console-formatted report
   */
  private static generateConsoleReport(report: AnalysisReport): string {
    const lines: string[] = [];
    
    // Header
    lines.push(chalk.bold.cyan('═'.repeat(80)));
    lines.push(chalk.bold.cyan('🎯 CS2 BETTING ANALYTICS - ULTIMATE ANALYSIS REPORT'));
    lines.push(chalk.bold.cyan('═'.repeat(80)));
    lines.push('');
    
    // Metadata
    lines.push(chalk.bold.yellow('📊 ANALYSIS METADATA'));
    lines.push(chalk.gray('─'.repeat(40)));
    lines.push(`📅 Target Date: ${chalk.white(report.metadata.targetDate)}`);
    lines.push(`🕐 Analysis Time: ${chalk.white(DateUtils.formatForDisplay(report.metadata.analysisDate))}`);
    lines.push(`⚡ Execution Time: ${chalk.white(report.metadata.executionTime)}ms`);
    lines.push(`🎮 Total Matches: ${chalk.white(report.metadata.totalMatches)}`);
    lines.push(`✅ Qualifying Matches: ${chalk.white(report.metadata.qualifyingMatches)}`);
    lines.push(`📡 API Requests: ${chalk.white(report.metadata.apiRequestsUsed)}`);
    lines.push('');
    
    // Top 5 Opportunities
    if (report.opportunities.length > 0) {
      lines.push(chalk.bold.green('🏆 TOP 5 BETTING OPPORTUNITIES'));
      lines.push(chalk.gray('═'.repeat(80)));
      lines.push('');
      
      report.opportunities.slice(0, 5).forEach((opp, index) => {
        lines.push(...this.formatOpportunity(opp, index + 1));
        if (index < 4) lines.push('');
      });
    } else {
      lines.push(chalk.bold.red('❌ NO BETTING OPPORTUNITIES FOUND'));
      lines.push(chalk.yellow('No positive EV opportunities identified for the target date.'));
      lines.push('');
    }
    
    // Summary Statistics
    lines.push(chalk.bold.blue('📈 SUMMARY STATISTICS'));
    lines.push(chalk.gray('─'.repeat(40)));
    lines.push(`🛡️  Low Risk Bets: ${chalk.green(report.summary.riskDistribution.low)}`);
    lines.push(`⚖️  Medium Risk Bets: ${chalk.yellow(report.summary.riskDistribution.medium)}`);
    lines.push(`🚫 High Risk Bets: ${chalk.red(report.summary.riskDistribution.high)}`);
    lines.push('');
    
    // Market Inefficiencies
    if (report.summary.marketInefficiencies.length > 0) {
      lines.push(chalk.bold.magenta('💎 MARKET INEFFICIENCIES DETECTED'));
      lines.push(chalk.gray('─'.repeat(40)));
      report.summary.marketInefficiencies.forEach(inefficiency => {
        lines.push(`• ${chalk.white(inefficiency)}`);
      });
      lines.push('');
    }
    
    // Data Quality Assessment
    lines.push(chalk.bold.cyan('🔍 DATA QUALITY ASSESSMENT'));
    lines.push(chalk.gray('─'.repeat(40)));
    lines.push(`📊 Overall Completeness: ${this.colorizePercentage(report.dataQuality.overallCompleteness)}%`);
    lines.push(`🎯 Reliability Score: ${this.colorizePercentage(report.dataQuality.reliabilityScore)}%`);
    
    if (report.dataQuality.missingDataPoints.length > 0) {
      lines.push(`⚠️  Missing Data Points:`);
      report.dataQuality.missingDataPoints.forEach(point => {
        lines.push(`   • ${chalk.yellow(point)}`);
      });
    }
    
    if (report.dataQuality.recommendations.length > 0) {
      lines.push(`💡 Recommendations:`);
      report.dataQuality.recommendations.forEach(rec => {
        lines.push(`   • ${chalk.cyan(rec)}`);
      });
    }
    lines.push('');
    
    // Warnings
    if (report.warnings.length > 0) {
      lines.push(chalk.bold.red('⚠️  WARNINGS'));
      lines.push(chalk.gray('─'.repeat(40)));
      report.warnings.forEach(warning => {
        lines.push(`• ${chalk.yellow(warning)}`);
      });
      lines.push('');
    }
    
    // Disclaimers
    lines.push(chalk.bold.gray('📋 DISCLAIMERS'));
    lines.push(chalk.gray('─'.repeat(40)));
    report.disclaimers.forEach(disclaimer => {
      lines.push(`• ${chalk.gray(disclaimer)}`);
    });
    lines.push('');
    
    // Footer
    lines.push(chalk.bold.cyan('═'.repeat(80)));
    lines.push(chalk.bold.cyan('🚀 END OF ANALYSIS - TRADE RESPONSIBLY'));
    lines.push(chalk.bold.cyan('═'.repeat(80)));
    
    return lines.join('\n');
  }
  
  /**
   * Format individual betting opportunity for console
   */
  private static formatOpportunity(opp: BettingOpportunity, rank: number): string[] {
    const lines: string[] = [];
    
    // Header
    const riskEmoji = opp.risk.level === 'low' ? '🛡️' : opp.risk.level === 'medium' ? '⚖️' : '🚫';
    lines.push(chalk.bold.white(`RANK #${rank}: ${opp.match.team1} vs ${opp.match.team2} — Bet: ${opp.bet.type.replace('_', ' ').toUpperCase()}`));
    
    // Basic Info
    lines.push(`Tournament: ${chalk.cyan(opp.match.tournament)} (${opp.match.stage})`);
    lines.push(`Date/Time: ${chalk.white(DateUtils.formatForUTC(opp.match.date))}`);
    lines.push(`Odds: ${chalk.white(opp.bet.odds)} (Source: ${opp.bet.oddsSource})`);
    lines.push(`Risk Level: ${riskEmoji} ${chalk.white(opp.risk.level.charAt(0).toUpperCase() + opp.risk.level.slice(1))}`);
    lines.push(`Confidence: ${this.colorizePercentage(opp.analysis.confidence)}%`);
    lines.push(`Expected Value: ${this.colorizeEV(opp.analysis.expectedValue)}%`);
    lines.push('');
    
    // Analysis Summary
    lines.push(chalk.bold.yellow('ANALYSIS SUMMARY:'));
    lines.push(`• Team Rankings: ${chalk.white(opp.analysis.summary.teamRankings)}`);
    lines.push(`• Recent Form: ${chalk.white(opp.analysis.summary.recentForm)}`);
    
    if (opp.analysis.summary.mapPoolAdvantage.length > 0) {
      lines.push(`• Map Pool Advantage:`);
      opp.analysis.summary.mapPoolAdvantage.forEach(advantage => {
        lines.push(`  ${chalk.white(advantage)}`);
      });
    }
    
    if (opp.analysis.summary.keyPlayerMatchups.length > 0) {
      lines.push(`• Key Player Matchups: ${chalk.white(opp.analysis.summary.keyPlayerMatchups.join(' vs '))}`);
    }
    
    if (opp.analysis.summary.advancedMetrics.length > 0) {
      lines.push(`• Advanced Metrics:`);
      opp.analysis.summary.advancedMetrics.forEach(metric => {
        lines.push(`  ${chalk.white(metric)}`);
      });
    }
    
    if (opp.analysis.summary.tournamentContext.length > 0) {
      lines.push(`• Tournament Context:`);
      opp.analysis.summary.tournamentContext.forEach(context => {
        lines.push(`  ${chalk.white(context)}`);
      });
    }
    
    lines.push('');
    
    // Justification
    lines.push(chalk.bold.green('JUSTIFICATION:'));
    lines.push(chalk.white(opp.analysis.justification));
    lines.push('');
    
    // Red Flags
    if (opp.analysis.redFlags.length > 0) {
      lines.push(chalk.bold.red('RED FLAGS:'));
      opp.analysis.redFlags.forEach(flag => {
        lines.push(`• ${chalk.red(flag)}`);
      });
      lines.push('');
    }
    
    return lines;
  }
  
  /**
   * Generate JSON report
   */
  private static generateJsonReport(report: AnalysisReport): string {
    return JSON.stringify(report, null, 2);
  }
  
  /**
   * Generate HTML report
   */
  private static generateHtmlReport(report: AnalysisReport): string {
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CS2 Betting Analytics Report - ${report.metadata.targetDate}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #1a1a1a; color: #ffffff; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; }
        .header h1 { margin: 0; font-size: 2.5em; text-shadow: 2px 2px 4px rgba(0,0,0,0.5); }
        .metadata { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .metadata-item { background: #2a2a2a; padding: 15px; border-radius: 8px; border-left: 4px solid #667eea; }
        .opportunity { background: #2a2a2a; margin-bottom: 20px; padding: 20px; border-radius: 10px; border: 1px solid #444; }
        .opportunity-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .rank { background: #667eea; color: white; padding: 5px 15px; border-radius: 20px; font-weight: bold; }
        .risk-low { color: #4CAF50; }
        .risk-medium { color: #FF9800; }
        .risk-high { color: #F44336; }
        .ev-positive { color: #4CAF50; font-weight: bold; }
        .ev-negative { color: #F44336; font-weight: bold; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #2a2a2a; padding: 20px; border-radius: 10px; border-top: 4px solid #667eea; }
        .footer { text-align: center; margin-top: 40px; padding: 20px; background: #2a2a2a; border-radius: 10px; }
        .disclaimer { font-size: 0.9em; color: #aaa; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 CS2 Betting Analytics Report</h1>
            <p>Ultimate Analysis for ${report.metadata.targetDate}</p>
        </div>
        
        <div class="metadata">
            <div class="metadata-item">
                <strong>Analysis Date:</strong><br>
                ${DateUtils.formatForDisplay(report.metadata.analysisDate)}
            </div>
            <div class="metadata-item">
                <strong>Total Matches:</strong><br>
                ${report.metadata.totalMatches}
            </div>
            <div class="metadata-item">
                <strong>Qualifying Matches:</strong><br>
                ${report.metadata.qualifyingMatches}
            </div>
            <div class="metadata-item">
                <strong>Execution Time:</strong><br>
                ${report.metadata.executionTime}ms
            </div>
        </div>
        
        ${report.opportunities.length > 0 ? `
        <h2>🏆 Top Betting Opportunities</h2>
        ${report.opportunities.slice(0, 5).map(opp => `
        <div class="opportunity">
            <div class="opportunity-header">
                <div class="rank">RANK #${opp.rank}</div>
                <div class="risk-${opp.risk.level}">Risk: ${opp.risk.level.toUpperCase()}</div>
            </div>
            <h3>${opp.match.team1} vs ${opp.match.team2}</h3>
            <p><strong>Bet:</strong> ${opp.bet.selection}</p>
            <p><strong>Odds:</strong> ${opp.bet.odds} | <strong>Expected Value:</strong> <span class="ev-positive">+${opp.analysis.expectedValue.toFixed(2)}%</span></p>
            <p><strong>Confidence:</strong> ${opp.analysis.confidence.toFixed(1)}%</p>
            <p><strong>Tournament:</strong> ${opp.match.tournament} (${opp.match.stage})</p>
            <p><strong>Justification:</strong> ${opp.analysis.justification}</p>
        </div>
        `).join('')}
        ` : '<h2>❌ No Betting Opportunities Found</h2>'}
        
        <div class="summary-grid">
            <div class="summary-card">
                <h3>📊 Risk Distribution</h3>
                <p>🛡️ Low Risk: ${report.summary.riskDistribution.low}</p>
                <p>⚖️ Medium Risk: ${report.summary.riskDistribution.medium}</p>
                <p>🚫 High Risk: ${report.summary.riskDistribution.high}</p>
            </div>
            <div class="summary-card">
                <h3>🔍 Data Quality</h3>
                <p>Completeness: ${report.dataQuality.overallCompleteness.toFixed(1)}%</p>
                <p>Reliability: ${report.dataQuality.reliabilityScore.toFixed(1)}%</p>
            </div>
        </div>
        
        <div class="footer">
            ${report.disclaimers.map(disclaimer => `<div class="disclaimer">${disclaimer}</div>`).join('')}
        </div>
    </div>
</body>
</html>`;
    
    return html;
  }
  
  /**
   * Colorize percentage values for console output
   */
  private static colorizePercentage(value: number): string {
    if (value >= 80) return chalk.green(value.toFixed(1));
    if (value >= 60) return chalk.yellow(value.toFixed(1));
    return chalk.red(value.toFixed(1));
  }
  
  /**
   * Colorize expected value for console output
   */
  private static colorizeEV(value: number): string {
    if (value > 0) return chalk.green(`+${value.toFixed(2)}`);
    return chalk.red(value.toFixed(2));
  }
  
  /**
   * Display report to console
   */
  static displayReport(report: AnalysisReport): void {
    const consoleReport = this.generateConsoleReport(report);
    console.log(consoleReport);
  }
}
