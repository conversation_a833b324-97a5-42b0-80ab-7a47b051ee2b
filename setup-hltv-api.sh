#!/bin/bash

# CS2 Betting Analytics - HLTV API Setup Script
# This script clones and extends the eupeutro/hltv-api with additional endpoints

set -e

echo "🎯 Setting up HLTV API for CS2 Betting Analytics..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Git is installed
if ! command -v git &> /dev/null; then
    echo "❌ Git is not installed. Please install Git first."
    exit 1
fi

# Clone the HLTV API repository if it doesn't exist
if [ ! -d "hltv-api" ]; then
    echo "📥 Cloning eupeutro/hltv-api repository..."
    git clone https://github.com/eupeutro/hltv-api.git
else
    echo "📁 HLTV API directory already exists, updating..."
    cd hltv-api
    git pull origin main
    cd ..
fi

echo "🔧 Extending HLTV API with additional endpoints..."

# Create additional endpoint files
mkdir -p hltv-api/app/api/endpoints
mkdir -p hltv-api/app/services/matches
mkdir -p hltv-api/app/services/teams
mkdir -p hltv-api/app/services/rankings
mkdir -p hltv-api/app/schemas/matches
mkdir -p hltv-api/app/schemas/teams
mkdir -p hltv-api/app/schemas/rankings

echo "📝 Creating extended API endpoints..."

# We'll create the extended endpoints in the next steps
echo "✅ HLTV API setup completed!"
echo ""
echo "🚀 Next steps:"
echo "1. Run: docker-compose up --build"
echo "2. Access HLTV API at: http://localhost:8000"
echo "3. Access API docs at: http://localhost:8000/docs"
echo ""
echo "🎮 To run CS2 Betting Analytics:"
echo "docker-compose exec cs2-betting-analytics npm run analyze"
