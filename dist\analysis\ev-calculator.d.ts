import { BetType } from '@/models/betting';
import { TeamAnalysisResult } from './team-analyzer';
import { RiskAssessmentResult } from './risk-assessor';
export interface EVCalculationResult {
    betType: BetType;
    selection: string;
    odds: {
        decimal: number;
        impliedProbability: number;
        source: string;
    };
    assessment: {
        trueProbability: number;
        confidence: number;
        methodology: string[];
    };
    expectedValue: {
        absolute: number;
        percentage: number;
        isPositive: boolean;
    };
    kelly: {
        fraction: number;
        recommendedStake: number;
        maxStake: number;
    };
    factors: {
        strengths: string[];
        concerns: string[];
        keyAssumptions: string[];
    };
    recommendation: {
        action: 'bet' | 'pass' | 'monitor';
        reasoning: string;
        confidence: number;
    };
}
export interface MatchContext {
    team1Analysis: TeamAnalysisResult;
    team2Analysis: TeamAnalysisResult;
    riskAssessment: RiskAssessmentResult;
    matchFormat: string;
    significance: number;
    odds?: {
        team1: number;
        team2: number;
        source: string;
    };
}
export declare class EVCalculator {
    /**
     * Calculate expected value for match winner bet
     */
    static calculateMatchWinnerEV(context: MatchContext, favoredTeam: 'team1' | 'team2'): EVCalculationResult;
    /**
     * Calculate match win probability
     */
    private static calculateMatchWinProbability;
    /**
     * Calculate map handicap EV
     */
    static calculateMapHandicapEV(context: MatchContext, line: number, odds: number, favoredTeam: 'team1' | 'team2'): EVCalculationResult;
    /**
     * Calculate handicap covering probability
     */
    private static calculateHandicapProbability;
    /**
     * Calculate confidence in EV assessment
     */
    private static calculateConfidence;
    /**
     * Generate factors for match winner bet
     */
    private static generateMatchWinnerFactors;
    /**
     * Generate factors for handicap bet
     */
    private static generateHandicapFactors;
    /**
     * Generate betting recommendation
     */
    private static generateRecommendation;
}
//# sourceMappingURL=ev-calculator.d.ts.map