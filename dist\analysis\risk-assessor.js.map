{"version": 3, "file": "risk-assessor.js", "sourceRoot": "", "sources": ["../../src/analysis/risk-assessor.ts"], "names": [], "mappings": ";;;AAEA,gDAA2C;AAC3C,2CAAwC;AACxC,mDAA+C;AAiE/C,MAAa,YAAY;IAWvB;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,OAAyB;QACzC,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;YACnC,KAAK,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI;YACjC,KAAK,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI;YACjC,MAAM,EAAE,OAAO,CAAC,WAAW;SAC5B,CAAC,CAAC;QAEH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,8DAA8D;YAC9D,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,OAAO,IAAI,OAAO,CAAC,aAAa,CAAC,OAAO;gBAChF,CAAC,CAAC,OAAO,CAAC,aAAa;gBACvB,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;YAE1B,MAAM,YAAY,GAAG,WAAW,KAAK,OAAO,CAAC,aAAa;gBACxD,CAAC,CAAC,OAAO,CAAC,aAAa;gBACvB,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;YAE1B,wBAAwB;YACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;YAE3E,8BAA8B;YAC9B,MAAM,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;YAExF,uBAAuB;YACvB,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAEhD,uBAAuB;YACvB,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;YAE7E,uCAAuC;YACvC,MAAM,iBAAiB,GAAG,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;YAC7F,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;YAEjF,0BAA0B;YAC1B,MAAM,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;YAEvF,qCAAqC;YACrC,MAAM,UAAU,GAAG,IAAI,CAAC,6BAA6B,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YAEjF,MAAM,MAAM,GAAyB;gBACnC,SAAS;gBACT,KAAK,EAAE,sBAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;gBAChC,QAAQ;gBACR,gBAAgB;gBAChB,iBAAiB;gBACjB,WAAW;gBACX,cAAc;gBACd,UAAU,EAAE,sBAAS,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC;aAC3C,CAAC;YAEF,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,eAAM,CAAC,WAAW,CAAC,iBAAiB,EAAE,QAAQ,EAAE;gBAC9C,KAAK,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI;gBACjC,KAAK,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI;gBACjC,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,KAAK,EAAE,MAAM,CAAC,KAAK;aACpB,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAc,EAAE;gBACrD,KAAK,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI;gBACjC,KAAK,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI;aAClC,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gBAAgB,CAC7B,WAA+B,EAC/B,YAAgC,EAChC,OAAyB;QAEzB,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;QAExE,OAAO;YACL,UAAU,EAAE;gBACV,KAAK,EAAE,UAAU;gBACjB,KAAK,EAAE,UAAU,IAAI,iBAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU;gBAClD,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU;aACzC;YAED,kBAAkB,EAAE;gBAClB,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO;gBACtC,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,iBAAM,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB;gBAC/E,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,kBAAkB;aACjD;YAED,eAAe,EAAE;gBACf,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,SAAS;gBACnC,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,SAAS,IAAI,EAAE,EAAE,0BAA0B;gBACrE,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,eAAe;aAC9C;YAED,WAAW,EAAE;gBACX,KAAK,EAAE,OAAO,CAAC,WAAW;gBAC1B,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;gBACjE,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,WAAW;aAC1C;YAED,gBAAgB,EAAE;gBAChB,KAAK,EAAE,WAAW,CAAC,OAAO,CAAC,gBAAgB;gBAC3C,KAAK,EAAE,WAAW,CAAC,OAAO,CAAC,gBAAgB,IAAI,EAAE,EAAE,gBAAgB;gBACnE,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB;aAC/C;YAED,UAAU,EAAE;gBACV,KAAK,EAAE,WAAW,CAAC,aAAa;gBAChC,KAAK,EAAE,WAAW,CAAC,aAAa,IAAI,iBAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU;gBACjE,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU;aACzC;YAED,aAAa,EAAE;gBACb,KAAK,EAAE,WAAW,CAAC,WAAW,CAAC,YAAY;gBAC3C,KAAK,EAAE,WAAW,CAAC,WAAW,CAAC,YAAY,IAAI,iBAAM,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa;gBAC/E,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,aAAa;aAC5C;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAClC,WAA+B,EAC/B,YAAgC,EAChC,OAAyB;QAEzB,MAAM,OAAO,GAA6C,EAAE,CAAC;QAE7D,yBAAyB;QACzB,IAAI,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,iBAAM,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;YACvE,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,qBAAqB;gBAC7B,MAAM,EAAE,0BAA0B,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,4BAA4B;gBAC7F,QAAQ,EAAE,MAAM;aACjB,CAAC,CAAC;QACL,CAAC;QAED,+BAA+B;QAC/B,IAAI,WAAW,CAAC,MAAM,CAAC,SAAS,GAAG,EAAE,EAAE,CAAC;YACtC,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,oBAAoB;gBAC5B,MAAM,EAAE,mCAAmC,WAAW,CAAC,MAAM,CAAC,SAAS,cAAc;gBACrF,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;QACL,CAAC;QAED,yBAAyB;QACzB,IAAI,WAAW,CAAC,MAAM,CAAC,aAAa,GAAG,iBAAM,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;YAC7E,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,oBAAoB;gBAC5B,MAAM,EAAE,wBAAwB,WAAW,CAAC,MAAM,CAAC,aAAa,mBAAmB;gBACnF,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;QACL,CAAC;QAED,qBAAqB;QACrB,IAAI,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7F,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,cAAc;gBACtB,MAAM,EAAE,4CAA4C;gBACpD,QAAQ,EAAE,MAAM;aACjB,CAAC,CAAC;QACL,CAAC;QAED,oBAAoB;QACpB,IAAI,WAAW,CAAC,WAAW,CAAC,WAAW,KAAK,KAAK,IAAI,YAAY,CAAC,WAAW,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;YACpG,OAAO,CAAC,IAAI,CAAC;gBACX,MAAM,EAAE,mBAAmB;gBAC3B,MAAM,EAAE,wCAAwC;gBAChD,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,kBAAkB,CAAC,QAA0C;QAC1E,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,WAAW,GAAG,CAAC,CAAC;QAEpB,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YAC1C,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,UAAU,IAAI,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC;YACvC,WAAW,IAAI,SAAS,CAAC,MAAM,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,OAAO,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,kBAAkB,CAC/B,QAA0C,EAC1C,gBAA0D,EAC1D,KAAa;QAEb,uCAAuC;QACvC,MAAM,sBAAsB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC;QACnF,IAAI,sBAAsB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtC,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,gDAAgD;QAChD,MAAM,eAAe,GAAG;YACtB,QAAQ,CAAC,UAAU,CAAC,KAAK,IAAI,iBAAM,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU;YACvD,QAAQ,CAAC,kBAAkB,CAAC,KAAK,IAAI,iBAAM,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB;YACvE,QAAQ,CAAC,eAAe,CAAC,KAAK;YAC9B,QAAQ,CAAC,WAAW,CAAC,KAAK;YAC1B,QAAQ,CAAC,gBAAgB,CAAC,KAAK,IAAI,iBAAM,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB;YACnE,QAAQ,CAAC,UAAU,CAAC,KAAK,IAAI,iBAAM,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU;YACvD,QAAQ,CAAC,aAAa,CAAC,KAAK,IAAI,iBAAM,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa;SAC9D,CAAC;QAEF,MAAM,aAAa,GAAG,eAAe,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACrD,MAAM,0BAA0B,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;QAEtG,IAAI,aAAa,IAAI,0BAA0B,IAAI,KAAK,IAAI,EAAE,EAAE,CAAC;YAC/D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,iCAAiC;QACjC,MAAM,kBAAkB,GAAG;YACzB,QAAQ,CAAC,UAAU,CAAC,KAAK,IAAI,iBAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU;YAC1D,QAAQ,CAAC,kBAAkB,CAAC,KAAK,IAAI,iBAAM,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC1E,QAAQ,CAAC,UAAU,CAAC,KAAK,IAAI,iBAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU;SAC3D,CAAC;QAEF,MAAM,aAAa,GAAG,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC;QAErE,IAAI,aAAa,IAAI,KAAK,IAAI,EAAE,EAAE,CAAC;YACjC,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,yBAAyB,CACtC,WAA+B,EAC/B,YAAgC,EAChC,OAAyB;QAEzB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;YAClD,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC9C,OAAO,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;YACnD,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,OAAO,CAAC,YAAY,IAAI,CAAC,EAAE,CAAC;YAC9B,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,WAAW,CAAC,iBAAiB,CAAC,eAAe,IAAI,EAAE,EAAE,CAAC;YACxD,OAAO,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAChC,WAA+B,EAC/B,YAAgC,EAChC,OAAyB;QAEzB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,EAAE,EAAE,CAAC;YAC7C,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;YACnD,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,KAAK,EAAE,CAAC;YAChD,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,WAAW,CAAC,iBAAiB,CAAC,YAAY,GAAG,EAAE,EAAE,CAAC;YACpD,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACxD,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,6BAA6B,CAAC,EAAE,CAAC;YACnE,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,sBAAsB,CACnC,SAAoB,EACpB,gBAA0D,EAC1D,KAAa;QAEb,MAAM,sBAAsB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM,CAAC;QAE1F,IAAI,sBAAsB,GAAG,CAAC,IAAI,SAAS,KAAK,MAAM,EAAE,CAAC;YACvD,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,IAAI,SAAS,KAAK,KAAK,IAAI,KAAK,IAAI,EAAE,EAAE,CAAC;YACvC,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,6BAA6B,CAC1C,WAA+B,EAC/B,YAAgC;QAEhC,MAAM,OAAO,GAAG;YACd,WAAW,CAAC,WAAW,CAAC,YAAY;YACpC,YAAY,CAAC,WAAW,CAAC,YAAY;YACrC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,WAAW,CAAC,UAAU,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,EAAE,qBAAqB;YACjF,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,WAAW,CAAC,UAAU,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG;SAC5D,CAAC;QAEF,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAErC,OAAO,sBAAS,CAAC,KAAK,CAAC,sBAAS,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,OAAyB;QACnD,MAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,OAAO,IAAI,OAAO,CAAC,aAAa,CAAC,OAAO;YAChF,CAAC,CAAC,OAAO,CAAC,aAAa;YACvB,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;QAE1B,6BAA6B;QAC7B,MAAM,YAAY,GAAG;YACnB,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE,EAAE,+BAA+B;YACtE,WAAW,CAAC,WAAW,CAAC,WAAW,KAAK,KAAK,EAAE,wBAAwB;YACvE,WAAW,CAAC,MAAM,CAAC,SAAS,IAAI,EAAE,EAAE,2BAA2B;SAChE,CAAC;QAEF,OAAO,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;;AAvYH,oCAwYC;AAvYyB,6BAAgB,GAAG;IACzC,UAAU,EAAE,IAAI;IAChB,kBAAkB,EAAE,IAAI;IACxB,eAAe,EAAE,IAAI;IACrB,WAAW,EAAE,IAAI;IACjB,gBAAgB,EAAE,IAAI;IACtB,UAAU,EAAE,IAAI;IAChB,aAAa,EAAE,IAAI;CACpB,CAAC"}