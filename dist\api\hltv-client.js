"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HLTVClient = void 0;
const hltv_1 = __importDefault(require("hltv"));
const settings_1 = require("@/config/settings");
const logger_1 = require("@/utils/logger");
class HLTVClient {
    constructor() {
        this.requestCount = 0;
        this.lastRequestTime = 0;
        logger_1.Logger.info('HLTV Client initialized', {
            requestDelay: settings_1.config.hltv.requestDelay,
            maxConcurrentRequests: settings_1.config.hltv.maxConcurrentRequests,
            retryAttempts: settings_1.config.hltv.retryAttempts,
        });
    }
    /**
     * Rate limiting wrapper for HLTV requests
     */
    async rateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        if (timeSinceLastRequest < settings_1.config.hltv.requestDelay) {
            const waitTime = settings_1.config.hltv.requestDelay - timeSinceLastRequest;
            logger_1.Logger.debug('Rate limiting - waiting', { waitTime });
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
        this.lastRequestTime = Date.now();
        this.requestCount++;
    }
    /**
     * Execute HLTV request with retry logic
     */
    async executeRequest(requestFn, endpoint, options = {}) {
        const { retries = settings_1.config.hltv.retryAttempts, delay = settings_1.config.hltv.retryDelay } = options;
        await this.rateLimit();
        const startTime = Date.now();
        logger_1.Logger.apiRequest(endpoint);
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                const result = await requestFn();
                const duration = Date.now() - startTime;
                logger_1.Logger.apiResponse(endpoint, duration, true);
                logger_1.Logger.performance('hltv_request', duration, { endpoint, attempt });
                return result;
            }
            catch (error) {
                const duration = Date.now() - startTime;
                const isLastAttempt = attempt === retries;
                if (isLastAttempt) {
                    logger_1.Logger.apiResponse(endpoint, duration, false, error.message);
                    logger_1.Logger.error(`HLTV request failed after ${retries} attempts`, error, {
                        endpoint,
                        attempts: retries,
                    });
                    throw error;
                }
                logger_1.Logger.warn(`HLTV request attempt ${attempt} failed, retrying`, {
                    endpoint,
                    error: error.message,
                    nextAttemptIn: delay,
                });
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        throw new Error(`Failed to execute request after ${retries} attempts`);
    }
    /**
     * Get matches for a specific date
     */
    async getMatches(date) {
        const endpoint = 'getMatches';
        return this.executeRequest(() => hltv_1.default.getMatches(), endpoint);
    }
    /**
     * Get detailed match information
     */
    async getMatch(matchId) {
        const endpoint = `getMatch/${matchId}`;
        return this.executeRequest(() => hltv_1.default.getMatch({ id: matchId }), endpoint);
    }
    /**
     * Get team ranking
     */
    async getTeamRanking(options) {
        const endpoint = 'getTeamRanking';
        return this.executeRequest(() => hltv_1.default.getTeamRanking(options), endpoint);
    }
    /**
     * Get team information
     */
    async getTeam(teamId) {
        const endpoint = `getTeam/${teamId}`;
        return this.executeRequest(() => hltv_1.default.getTeam({ id: teamId }), endpoint);
    }
    /**
     * Get team statistics
     */
    async getTeamStats(teamId, options) {
        const endpoint = `getTeamStats/${teamId}`;
        return this.executeRequest(() => hltv_1.default.getTeamStats({ id: teamId, ...options }), endpoint);
    }
    /**
     * Get player information
     */
    async getPlayer(playerId) {
        const endpoint = `getPlayer/${playerId}`;
        return this.executeRequest(() => hltv_1.default.getPlayer({ id: playerId }), endpoint);
    }
    /**
     * Get player statistics
     */
    async getPlayerStats(playerId, options) {
        const endpoint = `getPlayerStats/${playerId}`;
        return this.executeRequest(() => hltv_1.default.getPlayerStats({ id: playerId, ...options }), endpoint);
    }
    /**
     * Get player ranking
     */
    async getPlayerRanking(options) {
        const endpoint = 'getPlayerRanking';
        return this.executeRequest(() => hltv_1.default.getPlayerRanking(options), endpoint);
    }
    /**
     * Get match results
     */
    async getResults(options) {
        const endpoint = 'getResults';
        return this.executeRequest(() => hltv_1.default.getResults(options || {}), endpoint);
    }
    /**
     * Get events
     */
    async getEvents(options) {
        const endpoint = 'getEvents';
        return this.executeRequest(() => hltv_1.default.getEvents(options), endpoint);
    }
    /**
     * Get event details
     */
    async getEvent(eventId) {
        const endpoint = `getEvent/${eventId}`;
        return this.executeRequest(() => hltv_1.default.getEvent({ id: eventId }), endpoint);
    }
    /**
     * Get match statistics
     */
    async getMatchStats(matchId) {
        const endpoint = `getMatchStats/${matchId}`;
        return this.executeRequest(() => hltv_1.default.getMatchStats({ id: matchId }), endpoint);
    }
    /**
     * Get request statistics
     */
    getRequestStats() {
        return {
            count: this.requestCount,
            lastRequestTime: this.lastRequestTime,
        };
    }
    /**
     * Reset request statistics
     */
    resetStats() {
        this.requestCount = 0;
        this.lastRequestTime = 0;
        logger_1.Logger.debug('HLTV client stats reset');
    }
}
exports.HLTVClient = HLTVClient;
//# sourceMappingURL=hltv-client.js.map