"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MathUtils = void 0;
class MathUtils {
    /**
     * Calculate percentage with specified decimal places
     */
    static percentage(value, total, decimals = 1) {
        if (total === 0)
            return 0;
        return Math.round((value / total) * 100 * Math.pow(10, decimals)) / Math.pow(10, decimals);
    }
    /**
     * Calculate win rate from wins and losses
     */
    static winRate(wins, losses) {
        const total = wins + losses;
        return total === 0 ? 0 : this.percentage(wins, total, 1);
    }
    /**
     * Calculate implied probability from decimal odds
     */
    static impliedProbability(decimalOdds) {
        return this.percentage(1, decimalOdds, 2);
    }
    /**
     * Calculate expected value
     * EV = (Probability of Win × Amount Won per Bet) - (Probability of Loss × Amount Lost per Bet)
     */
    static expectedValue(assessedProbability, decimalOdds, stake = 100) {
        const probabilityDecimal = assessedProbability / 100;
        const probabilityLoss = 1 - probabilityDecimal;
        const amountWon = (decimalOdds - 1) * stake;
        const amountLost = stake;
        const ev = (probabilityDecimal * amountWon) - (probabilityLoss * amountLost);
        return Math.round(ev * 100) / 100;
    }
    /**
     * Calculate expected value as percentage
     */
    static expectedValuePercentage(assessedProbability, decimalOdds) {
        const ev = this.expectedValue(assessedProbability, decimalOdds, 100);
        return Math.round(ev * 100) / 100;
    }
    /**
     * Check if bet has positive expected value
     */
    static hasPositiveEV(assessedProbability, decimalOdds) {
        return this.expectedValue(assessedProbability, decimalOdds) > 0;
    }
    /**
     * Calculate Kelly Criterion optimal bet size
     */
    static kellyCriterion(assessedProbability, decimalOdds, bankroll) {
        const p = assessedProbability / 100;
        const b = decimalOdds - 1;
        const q = 1 - p;
        const kellyFraction = (b * p - q) / b;
        return Math.max(0, kellyFraction * bankroll);
    }
    /**
     * Calculate average with weights
     */
    static weightedAverage(values, weights) {
        if (values.length !== weights.length) {
            throw new Error('Values and weights arrays must have the same length');
        }
        const weightedSum = values.reduce((sum, value, index) => sum + value * weights[index], 0);
        const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
        return totalWeight === 0 ? 0 : weightedSum / totalWeight;
    }
    /**
     * Calculate standard deviation
     */
    static standardDeviation(values) {
        if (values.length === 0)
            return 0;
        const mean = values.reduce((sum, value) => sum + value, 0) / values.length;
        const squaredDifferences = values.map(value => Math.pow(value - mean, 2));
        const variance = squaredDifferences.reduce((sum, diff) => sum + diff, 0) / values.length;
        return Math.sqrt(variance);
    }
    /**
     * Calculate coefficient of variation (relative standard deviation)
     */
    static coefficientOfVariation(values) {
        if (values.length === 0)
            return 0;
        const mean = values.reduce((sum, value) => sum + value, 0) / values.length;
        const stdDev = this.standardDeviation(values);
        return mean === 0 ? 0 : (stdDev / mean) * 100;
    }
    /**
     * Calculate confidence interval
     */
    static confidenceInterval(values, confidenceLevel = 0.95) {
        if (values.length === 0) {
            return { lower: 0, upper: 0, mean: 0 };
        }
        const mean = values.reduce((sum, value) => sum + value, 0) / values.length;
        const stdDev = this.standardDeviation(values);
        const standardError = stdDev / Math.sqrt(values.length);
        // Using t-distribution approximation for 95% confidence
        const tValue = 1.96; // For large samples and 95% confidence
        const marginOfError = tValue * standardError;
        return {
            lower: mean - marginOfError,
            upper: mean + marginOfError,
            mean: mean,
        };
    }
    /**
     * Normalize value to 0-100 scale
     */
    static normalize(value, min, max) {
        if (max === min)
            return 50; // Return middle value if no range
        return Math.max(0, Math.min(100, ((value - min) / (max - min)) * 100));
    }
    /**
     * Calculate moving average
     */
    static movingAverage(values, window) {
        if (window <= 0 || window > values.length) {
            throw new Error('Invalid window size');
        }
        const result = [];
        for (let i = window - 1; i < values.length; i++) {
            const windowValues = values.slice(i - window + 1, i + 1);
            const average = windowValues.reduce((sum, val) => sum + val, 0) / window;
            result.push(average);
        }
        return result;
    }
    /**
     * Round to specified decimal places
     */
    static round(value, decimals = 2) {
        return Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals);
    }
    /**
     * Clamp value between min and max
     */
    static clamp(value, min, max) {
        return Math.max(min, Math.min(max, value));
    }
}
exports.MathUtils = MathUtils;
//# sourceMappingURL=math-utils.js.map