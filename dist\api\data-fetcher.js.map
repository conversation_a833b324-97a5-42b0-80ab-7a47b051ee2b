{"version": 3, "file": "data-fetcher.js", "sourceRoot": "", "sources": ["../../src/api/data-fetcher.ts"], "names": [], "mappings": ";;;AAAA,+CAA2C;AAC3C,4DAAwD;AACxD,2CAAwC;AACxC,mDAA+C;AAW/C,MAAa,WAAW;IAGtB;QACE,IAAI,CAAC,UAAU,GAAG,IAAI,wBAAU,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,IAAU,EAAE,UAA8B,EAAE;QAC7D,MAAM,EAAE,QAAQ,GAAG,IAAI,EAAE,YAAY,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;QAC1D,MAAM,OAAO,GAAG,sBAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAE9C,eAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC,CAAC;QAE3E,IAAI,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;YAC9B,OAAO,4BAAY,CAAC,QAAQ,CAC1B,SAAS,EACT,OAAO,EACP,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CACvC,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAEvD,IAAI,QAAQ,EAAE,CAAC;YACb,4BAAY,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAChD,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC7B,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,OAAO,CAAC,MAAM;SACtB,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,OAAe,EAAE,UAA8B,EAAE;QACvE,MAAM,EAAE,QAAQ,GAAG,IAAI,EAAE,YAAY,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;QAE1D,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC,CAAC;QAE5E,IAAI,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;YAC9B,OAAO,4BAAY,CAAC,QAAQ,CAC1B,eAAe,EACf,OAAO,EACP,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CACxC,CAAC;QACJ,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAE7D,IAAI,QAAQ,EAAE,CAAC;YACb,4BAAY,CAAC,GAAG,CAAC,eAAe,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QAC3D,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,UAA8B,EAAE;QACtD,MAAM,EAAE,QAAQ,GAAG,IAAI,EAAE,YAAY,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;QAE1D,eAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC,CAAC;QAElE,IAAI,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;YAC9B,OAAO,4BAAY,CAAC,QAAQ,CAC1B,UAAU,EACV,OAAO,EACP,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CACvC,CAAC;QACJ,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;QAExD,IAAI,QAAQ,EAAE,CAAC;YACb,4BAAY,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAClD,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QAEjE,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,UAA8B,EAAE;QAIlE,MAAM,EAAE,QAAQ,GAAG,IAAI,EAAE,YAAY,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;QAE1D,eAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC,CAAC;QAEvE,wCAAwC;QACxC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACtC,QAAQ,IAAI,CAAC,YAAY;gBACvB,CAAC,CAAC,4BAAY,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAC/E,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;YAEnC,QAAQ,IAAI,CAAC,YAAY;gBACvB,CAAC,CAAC,4BAAY,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBACzF,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC;SACzC,CAAC,CAAC;QAEH,IAAI,QAAQ,IAAI,YAAY,EAAE,CAAC;YAC7B,4BAAY,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;YACxC,4BAAY,CAAC,GAAG,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QAChD,CAAC;QAED,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,UAA8B,EAAE;QAItE,MAAM,EAAE,QAAQ,GAAG,IAAI,EAAE,YAAY,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;QAE1D,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC,CAAC;QAE3E,0CAA0C;QAC1C,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACtC,QAAQ,IAAI,CAAC,YAAY;gBACvB,CAAC,CAAC,4BAAY,CAAC,QAAQ,CAAC,SAAS,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;gBACvF,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC;YAEvC,QAAQ,IAAI,CAAC,YAAY;gBACvB,CAAC,CAAC,4BAAY,CAAC,QAAQ,CAAC,cAAc,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBACjG,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC;SAC7C,CAAC,CAAC;QAEH,IAAI,QAAQ,IAAI,YAAY,EAAE,CAAC;YAC7B,4BAAY,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC5C,4BAAY,CAAC,GAAG,CAAC,cAAc,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,UAKA,EAAE;QACzB,MAAM,EAAE,QAAQ,GAAG,IAAI,EAAE,YAAY,GAAG,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;QAEnG,MAAM,MAAM,GAAQ,EAAE,CAAC;QACvB,IAAI,OAAO;YAAE,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;QACtC,IAAI,SAAS;YAAE,MAAM,CAAC,SAAS,GAAG,sBAAS,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QACrE,IAAI,OAAO;YAAE,MAAM,CAAC,OAAO,GAAG,sBAAS,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAE/D,eAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC,CAAC;QAE3E,MAAM,QAAQ,GAAG,kBAAkB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;QAE5D,IAAI,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;YAC9B,OAAO,4BAAY,CAAC,QAAQ,CAC1B,SAAS,EACT,QAAQ,EACR,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,CACzC,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAEzD,IAAI,QAAQ,EAAE,CAAC;YACb,4BAAY,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QACjD,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAEjE,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAAC,OAAe,EAAE,UAA8B,EAAE;QAO5E,eAAM,CAAC,IAAI,CAAC,4CAA4C,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QAEvE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,iDAAiD;QACjD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAE7D,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,0DAA0D,OAAO,EAAE,CAAC,CAAC;QACvF,CAAC;QAED,sCAAsC;QACtC,MAAM,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACxE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC;YAC3C,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC;YAC3C,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;YAC/B,IAAI,CAAC,kBAAkB,CAAC;gBACtB,OAAO,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzC,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,eAAe;gBAC3E,OAAO,EAAE,IAAI,IAAI,EAAE;gBACnB,KAAK,EAAE,EAAE;gBACT,GAAG,OAAO;aACX,CAAC;SACH,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAExC,eAAM,CAAC,WAAW,CAAC,2BAA2B,EAAE,QAAQ,EAAE;YACxD,OAAO;YACP,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI;YACvB,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI;SACxB,CAAC,CAAC;QAEH,OAAO;YACL,KAAK;YACL,SAAS;YACT,SAAS;YACT,QAAQ;YACR,aAAa;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,UAA8B,EAAE;QACxD,MAAM,EAAE,QAAQ,GAAG,IAAI,EAAE,YAAY,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;QAE1D,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC,CAAC;QAEpE,IAAI,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;YAC9B,OAAO,4BAAY,CAAC,QAAQ,CAC1B,UAAU,EACV,SAAS,EACT,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CACzC,CAAC;QACJ,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;QAE1D,IAAI,QAAQ,EAAE,CAAC;YACb,4BAAY,CAAC,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;QACpD,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QAEnE,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAC1B,OAAiB,EACjB,UAA8B,EAAE;QAEhC,MAAM,EAAE,qBAAqB,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;QAE9C,eAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;YACvC,OAAO;YACP,KAAK,EAAE,OAAO,CAAC,MAAM;YACrB,aAAa,EAAE,qBAAqB;SACrC,CAAC,CAAC;QAEH,MAAM,OAAO,GAAqD,EAAE,CAAC;QAErE,kDAAkD;QAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,qBAAqB,EAAE,CAAC;YAC/D,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,qBAAqB,CAAC,CAAC;YAE1D,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CACpC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;gBACzB,IAAI,CAAC;oBACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;oBACvD,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,CAAC;gBAC7B,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,MAAM,EAAE,EAAE,KAAc,CAAC,CAAC;oBACxE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;gBAC7C,CAAC;YACH,CAAC,CAAC,CACH,CAAC;YAEF,OAAO,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;YAE9B,8BAA8B;YAC9B,IAAI,CAAC,GAAG,qBAAqB,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;gBAC/C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE;YAC7C,SAAS,EAAE,OAAO,CAAC,MAAM;YACzB,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM;SAC1D,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC;QACpD,OAAO;YACL,QAAQ,EAAE,SAAS,CAAC,KAAK;YACzB,eAAe,EAAE,SAAS,CAAC,eAAe;YAC1C,UAAU,EAAE,4BAAY,CAAC,QAAQ,EAAE;SACpC,CAAC;IACJ,CAAC;CACF;AAxUD,kCAwUC"}