# 🎯 CS2 Betting Analytics System

The ultimate CS2 betting analytics system that leverages real HLTV data to identify high-value betting opportunities with maximum accuracy. This production-ready system uses exclusively real data from live API endpoints and implements a comprehensive betting framework for positive Expected Value (EV) identification.

## 🚀 Features

### Core Capabilities
- **Real-Time Data**: Uses the HLTV API for live, accurate match and team data
- **Risk Assessment**: Advanced 3-tier risk classification (Low/Medium/High)
- **Expected Value Calculation**: Sophisticated EV analysis with Kelly Criterion
- **Comprehensive Analysis**: Team performance, player statistics, and contextual factors
- **Multiple Bet Types**: Match winner, map handicap, total maps, and more
- **Date Flexibility**: Analyze today, tomorrow, or custom dates

### Advanced Analytics
- **Team Performance Metrics**: Opening kills, economy management, clutch performance
- **Player Analysis**: Role-specific performance, form trends, pressure handling
- **Map Pool Evaluation**: Strengths, weaknesses, and strategic advantages
- **Contextual Factors**: Patch adaptation, travel impact, motivation levels
- **Data Quality Assessment**: Completeness and reliability scoring

### Output & Reporting
- **Multiple Formats**: Console, JSON, and HTML reports
- **Detailed Analysis**: Comprehensive justification for each opportunity
- **Risk Warnings**: Clear identification of concerning factors
- **Market Inefficiencies**: Highlighted value opportunities

## 📋 Requirements

- **Node.js**: Version 18.0.0 or higher
- **Operating System**: Windows 11 compatible
- **Memory**: Minimum 4GB RAM recommended
- **Storage**: 1GB free space for cache and reports

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd cs2-bet
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Configure Environment
```bash
cp .env.example .env
# Edit .env file with your preferred settings
```

### 4. Build the Project
```bash
npm run build
```

## 🎮 Usage

### Basic Analysis Commands

#### Analyze Today's Matches
```bash
npm run analyze
# or
npm run analyze:today
```

#### Analyze Tomorrow's Matches
```bash
npm run analyze:tomorrow
```

#### Analyze Custom Date
```bash
npm run analyze:custom -- --date 2024-01-15
```

### Advanced Options

#### Different Output Formats
```bash
# Console output (default)
npm run dev -- analyze --format console

# JSON output
npm run dev -- analyze --format json

# HTML report
npm run dev -- analyze --format html
```

#### Save Reports
```bash
# Save report to file
npm run dev -- analyze --save

# Custom date with HTML report saved
npm run dev -- analyze --date 2024-01-15 --format html --save
```

#### Cache Management
```bash
# Show cache statistics
npm run dev -- cache --stats

# Clear all cache
npm run dev -- cache --clear

# Clear specific cache type
npm run dev -- cache --clear-type teams
```

### Development Commands

#### Test API Connectivity
```bash
npm run dev -- test
```

#### Show Configuration
```bash
npm run dev -- config
```

#### Run with Verbose Logging
```bash
npm run dev -- analyze --verbose
```

## ⚙️ Configuration

### Environment Variables

Key configuration options in `.env`:

```env
# Rate Limiting
HLTV_REQUEST_DELAY=1000
HLTV_MAX_CONCURRENT_REQUESTS=3

# Cache Settings
CACHE_TTL_MATCHES=300000
CACHE_TTL_TEAMS=1800000

# Analysis Thresholds
MIN_CONFIDENCE_THRESHOLD=60
MIN_POSITIVE_EV_THRESHOLD=0.05

# Risk Assessment
LOW_RISK_RANKING_GAP=10
MIN_WIN_RATE_LOW_RISK=70
```

### Risk Tiers

#### 🛡️ Low Risk Criteria (ALL must be met)
- HLTV ranking gap ≥ 10 places
- Favored team win rate ≥ 70% (last 10 matches)
- Stable roster (3+ months)
- BO3/BO5 format only
- Clear map pool advantage (≥65% win rate on ≥2 maps)
- Team average rating ≥ 1.10
- Pistol round win rate ≥ 55%

#### ⚖️ Medium Risk Criteria (ALL must be met)
- HLTV ranking gap ≥ 5 places
- Favored team win rate ≥ 60% (last 10 matches)
- Stable roster (1+ month)
- BO3/BO5 preferred
- Map pool advantage (≥60% win rate on ≥1 map)
- Team average rating ≥ 1.05
- Pistol round win rate ≥ 50%

#### 🚫 High Risk (Automatically Excluded)
- Teams with <55% recent win rate
- Major roster changes in past 30 days
- Insufficient verifiable data
- Key players with <0.95 rating in last 5 matches

## 📊 Output Format

### Console Report Example
```
═══════════════════════════════════════════════════════════════════════════════
🎯 CS2 BETTING ANALYTICS - ULTIMATE ANALYSIS REPORT
═══════════════════════════════════════════════════════════════════════════════

📊 ANALYSIS METADATA
────────────────────────────────────────
📅 Target Date: 2024-01-15 12:00
🕐 Analysis Time: 2024-01-15 10:30
⚡ Execution Time: 2847ms
🎮 Total Matches: 8
✅ Qualifying Matches: 5
📡 API Requests: 23

🏆 TOP 5 BETTING OPPORTUNITIES
═══════════════════════════════════════════════════════════════════════════════

RANK #1: Team A vs Team B — Bet: MATCH WINNER
Tournament: IEM Katowice 2024 (Playoffs)
Date/Time: 2024-01-15 14:00 UTC
Odds: 1.75 (Source: HLTV)
Risk Level: 🛡️ Low
Confidence: 82.5%
Expected Value: +8.3%

ANALYSIS SUMMARY:
• Team Rankings: Team A: #3 vs Team B: #15 (Gap: 12 places)
• Recent Form: Team A: 80.0% vs Team B: 40.0% (Last 10 matches)
• Map Pool Advantage: Team A strong on: Mirage, Inferno, Ancient
• Key Player Matchups: s1mple (1.25) vs device (1.08)
• Advanced Metrics: Opening kills: 62% vs 48%, Clutch: 35% vs 22%
• Tournament Context: Major tournament, BO3 format, High stakes

JUSTIFICATION:
Strong positive EV with high confidence and low risk assessment

RED FLAGS:
None identified
```

## 🧪 Testing

### Run Unit Tests
```bash
npm test
```

### Run Tests with Coverage
```bash
npm run test:coverage
```

### Watch Mode for Development
```bash
npm run test:watch
```

## 📁 Project Structure

```
cs2-bet/
├── src/
│   ├── analysis/          # Core analysis engines
│   │   ├── team-analyzer.ts
│   │   ├── risk-assessor.ts
│   │   ├── ev-calculator.ts
│   │   └── match-analyzer.ts
│   ├── api/               # HLTV API integration
│   │   ├── hltv-client.ts
│   │   └── data-fetcher.ts
│   ├── models/            # TypeScript interfaces
│   │   ├── match.ts
│   │   ├── team.ts
│   │   ├── player.ts
│   │   └── betting.ts
│   ├── services/          # Supporting services
│   │   ├── cache-service.ts
│   │   └── report-generator.ts
│   ├── utils/             # Utility functions
│   │   ├── date-utils.ts
│   │   ├── math-utils.ts
│   │   ├── validation.ts
│   │   └── logger.ts
│   ├── config/            # Configuration
│   │   └── settings.ts
│   └── main.ts            # Entry point
├── tests/                 # Test files
├── reports/               # Generated reports
├── logs/                  # Log files
└── docs/                  # Documentation
```

## 🔧 Development

### Code Quality
```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Type checking
npm run build
```

### Debugging
- Enable verbose logging with `--verbose` flag
- Check logs in `./logs/` directory
- Use cache statistics to monitor performance
- Test API connectivity with `npm run dev -- test`

## ⚠️ Important Disclaimers

- **Educational Purpose**: This system is for educational and analytical purposes only
- **No Guarantees**: Past performance does not guarantee future results
- **Responsible Gambling**: Always gamble responsibly and within your means
- **Data Accuracy**: While we use real HLTV data, accuracy cannot be 100% guaranteed
- **Risk Warning**: All betting involves risk of loss

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues, questions, or contributions:
- Check the documentation in `/docs`
- Review existing issues
- Create a new issue with detailed information
- Include logs and configuration when reporting bugs

## 🎯 Success Criteria

- **Accuracy**: All data verifiable against HLTV sources
- **Value Focus**: Only positive EV recommendations
- **Risk Management**: Clear tier classification
- **Usability**: Simple execution with comprehensive output
- **Reliability**: Consistent performance across different dates and tournaments

---

**Remember**: This tool identifies opportunities based on statistical analysis. Always conduct your own research and never bet more than you can afford to lose.
