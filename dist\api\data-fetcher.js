"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataFetcher = void 0;
const hltv_client_1 = require("./hltv-client");
const cache_service_1 = require("@/services/cache-service");
const logger_1 = require("@/utils/logger");
const date_utils_1 = require("@/utils/date-utils");
class DataFetcher {
    constructor() {
        this.hltvClient = new hltv_client_1.HLTVClient();
    }
    /**
     * Fetch all matches for a specific date
     */
    async fetchMatches(date, options = {}) {
        const { useCache = true, forceRefresh = false } = options;
        const dateStr = date_utils_1.DateUtils.formatForHLTV(date);
        logger_1.Logger.info('Fetching matches', { date: dateStr, useCache, forceRefresh });
        if (useCache && !forceRefresh) {
            return cache_service_1.cacheService.getOrSet('matches', dateStr, () => this.hltvClient.getMatches(date));
        }
        const matches = await this.hltvClient.getMatches(date);
        if (useCache) {
            cache_service_1.cacheService.set('matches', dateStr, matches);
        }
        logger_1.Logger.info('Matches fetched', {
            date: dateStr,
            count: matches.length,
        });
        return matches;
    }
    /**
     * Fetch detailed match information
     */
    async fetchMatchDetails(matchId, options = {}) {
        const { useCache = true, forceRefresh = false } = options;
        logger_1.Logger.debug('Fetching match details', { matchId, useCache, forceRefresh });
        if (useCache && !forceRefresh) {
            return cache_service_1.cacheService.getOrSet('match_details', matchId, () => this.hltvClient.getMatch(matchId));
        }
        const matchDetails = await this.hltvClient.getMatch(matchId);
        if (useCache) {
            cache_service_1.cacheService.set('match_details', matchId, matchDetails);
        }
        return matchDetails;
    }
    /**
     * Fetch team rankings
     */
    async fetchTeamRankings(options = {}) {
        const { useCache = true, forceRefresh = false } = options;
        logger_1.Logger.info('Fetching team rankings', { useCache, forceRefresh });
        if (useCache && !forceRefresh) {
            return cache_service_1.cacheService.getOrSet('rankings', 'teams', () => this.hltvClient.getTeamRanking());
        }
        const rankings = await this.hltvClient.getTeamRanking();
        if (useCache) {
            cache_service_1.cacheService.set('rankings', 'teams', rankings);
        }
        logger_1.Logger.info('Team rankings fetched', { count: rankings.length });
        return rankings;
    }
    /**
     * Fetch team information and statistics
     */
    async fetchTeamData(teamId, options = {}) {
        const { useCache = true, forceRefresh = false } = options;
        logger_1.Logger.debug('Fetching team data', { teamId, useCache, forceRefresh });
        // Fetch team info and stats in parallel
        const [info, stats] = await Promise.all([
            useCache && !forceRefresh
                ? cache_service_1.cacheService.getOrSet('teams', teamId, () => this.hltvClient.getTeam(teamId))
                : this.hltvClient.getTeam(teamId),
            useCache && !forceRefresh
                ? cache_service_1.cacheService.getOrSet('team_stats', teamId, () => this.hltvClient.getTeamStats(teamId))
                : this.hltvClient.getTeamStats(teamId),
        ]);
        if (useCache && forceRefresh) {
            cache_service_1.cacheService.set('teams', teamId, info);
            cache_service_1.cacheService.set('team_stats', teamId, stats);
        }
        return { info, stats };
    }
    /**
     * Fetch player information and statistics
     */
    async fetchPlayerData(playerId, options = {}) {
        const { useCache = true, forceRefresh = false } = options;
        logger_1.Logger.debug('Fetching player data', { playerId, useCache, forceRefresh });
        // Fetch player info and stats in parallel
        const [info, stats] = await Promise.all([
            useCache && !forceRefresh
                ? cache_service_1.cacheService.getOrSet('players', playerId, () => this.hltvClient.getPlayer(playerId))
                : this.hltvClient.getPlayer(playerId),
            useCache && !forceRefresh
                ? cache_service_1.cacheService.getOrSet('player_stats', playerId, () => this.hltvClient.getPlayerStats(playerId))
                : this.hltvClient.getPlayerStats(playerId),
        ]);
        if (useCache && forceRefresh) {
            cache_service_1.cacheService.set('players', playerId, info);
            cache_service_1.cacheService.set('player_stats', playerId, stats);
        }
        return { info, stats };
    }
    /**
     * Fetch recent match results for analysis
     */
    async fetchRecentResults(options = {}) {
        const { useCache = true, forceRefresh = false, teamIds, startDate, endDate, limit = 50 } = options;
        const params = {};
        if (teamIds)
            params.teamIds = teamIds;
        if (startDate)
            params.startDate = date_utils_1.DateUtils.formatForHLTV(startDate);
        if (endDate)
            params.endDate = date_utils_1.DateUtils.formatForHLTV(endDate);
        logger_1.Logger.info('Fetching recent results', { params, useCache, forceRefresh });
        const cacheKey = `recent_results_${JSON.stringify(params)}`;
        if (useCache && !forceRefresh) {
            return cache_service_1.cacheService.getOrSet('matches', cacheKey, () => this.hltvClient.getResults(params));
        }
        const results = await this.hltvClient.getResults(params);
        if (useCache) {
            cache_service_1.cacheService.set('matches', cacheKey, results);
        }
        logger_1.Logger.info('Recent results fetched', { count: results.length });
        return results.slice(0, limit);
    }
    /**
     * Fetch comprehensive data for a match analysis
     */
    async fetchMatchAnalysisData(matchId, options = {}) {
        logger_1.Logger.info('Fetching comprehensive match analysis data', { matchId });
        const startTime = Date.now();
        // First, get the match details to identify teams
        const match = await this.fetchMatchDetails(matchId, options);
        if (!match.team1?.id || !match.team2?.id) {
            throw new Error(`Invalid match data: missing team information for match ${matchId}`);
        }
        // Fetch all required data in parallel
        const [team1Data, team2Data, rankings, recentResults] = await Promise.all([
            this.fetchTeamData(match.team1.id, options),
            this.fetchTeamData(match.team2.id, options),
            this.fetchTeamRankings(options),
            this.fetchRecentResults({
                teamIds: [match.team1.id, match.team2.id],
                startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // Last 90 days
                endDate: new Date(),
                limit: 20,
                ...options,
            }),
        ]);
        const duration = Date.now() - startTime;
        logger_1.Logger.performance('match_analysis_data_fetch', duration, {
            matchId,
            team1: match.team1.name,
            team2: match.team2.name,
        });
        return {
            match,
            team1Data,
            team2Data,
            rankings,
            recentResults,
        };
    }
    /**
     * Fetch player rankings
     */
    async fetchPlayerRankings(options = {}) {
        const { useCache = true, forceRefresh = false } = options;
        logger_1.Logger.info('Fetching player rankings', { useCache, forceRefresh });
        if (useCache && !forceRefresh) {
            return cache_service_1.cacheService.getOrSet('rankings', 'players', () => this.hltvClient.getPlayerRanking());
        }
        const rankings = await this.hltvClient.getPlayerRanking();
        if (useCache) {
            cache_service_1.cacheService.set('rankings', 'players', rankings);
        }
        logger_1.Logger.info('Player rankings fetched', { count: rankings.length });
        return rankings;
    }
    /**
     * Batch fetch multiple teams data
     */
    async fetchMultipleTeamsData(teamIds, options = {}) {
        const { maxConcurrentRequests = 3 } = options;
        logger_1.Logger.info('Batch fetching teams data', {
            teamIds,
            count: teamIds.length,
            maxConcurrent: maxConcurrentRequests,
        });
        const results = [];
        // Process teams in batches to respect rate limits
        for (let i = 0; i < teamIds.length; i += maxConcurrentRequests) {
            const batch = teamIds.slice(i, i + maxConcurrentRequests);
            const batchResults = await Promise.all(batch.map(async (teamId) => {
                try {
                    const data = await this.fetchTeamData(teamId, options);
                    return { teamId, ...data };
                }
                catch (error) {
                    logger_1.Logger.error(`Failed to fetch data for team ${teamId}`, error);
                    return { teamId, info: null, stats: null };
                }
            }));
            results.push(...batchResults);
            // Small delay between batches
            if (i + maxConcurrentRequests < teamIds.length) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        logger_1.Logger.info('Batch team data fetch completed', {
            requested: teamIds.length,
            successful: results.filter(r => r.info && r.stats).length,
        });
        return results;
    }
    /**
     * Get client statistics
     */
    getClientStats() {
        const hltvStats = this.hltvClient.getRequestStats();
        return {
            requests: hltvStats.count,
            lastRequestTime: hltvStats.lastRequestTime,
            cacheStats: cache_service_1.cacheService.getStats(),
        };
    }
}
exports.DataFetcher = DataFetcher;
//# sourceMappingURL=data-fetcher.js.map