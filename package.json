{"name": "cs2-betting-analytics", "version": "1.0.0", "description": "Ultimate CS2 betting analytics system using HLTV data for high-value opportunity identification", "main": "dist/main.js", "scripts": {"build": "tsc", "start": "node dist/main.js", "dev": "ts-node -r tsconfig-paths/register src/main.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "analyze": "npm run dev", "analyze:today": "npm run dev -- --date today", "analyze:tomorrow": "npm run dev -- --date tomorrow", "analyze:custom": "npm run dev -- --date"}, "keywords": ["cs2", "csgo", "betting", "analytics", "hltv", "esports", "statistics", "expected-value"], "author": "CS2 Betting Analytics Team", "license": "MIT", "dependencies": {"chalk": "^4.1.2", "commander": "^11.1.0", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "hltv": "^3.5.0", "lodash": "^4.17.21", "node-cache": "^5.1.2", "tsconfig-paths": "^4.2.0", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.5", "@types/lodash": "^4.14.199", "@types/node": "^20.8.0", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "eslint": "^8.50.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}}