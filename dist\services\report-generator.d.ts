import { AnalysisReport } from '@/models/betting';
export type OutputFormat = 'console' | 'json' | 'html';
export declare class ReportGenerator {
    /**
     * Generate formatted report
     */
    static generateReport(report: AnalysisReport, format?: OutputFormat): string;
    /**
     * Save report to file
     */
    static saveReport(report: AnalysisReport, format?: OutputFormat): Promise<string>;
    /**
     * Generate console-formatted report
     */
    private static generateConsoleReport;
    /**
     * Format individual betting opportunity for console
     */
    private static formatOpportunity;
    /**
     * Generate JSON report
     */
    private static generateJsonReport;
    /**
     * Generate HTML report
     */
    private static generateHtmlReport;
    /**
     * Colorize percentage values for console output
     */
    private static colorizePercentage;
    /**
     * Colorize expected value for console output
     */
    private static colorizeEV;
    /**
     * Display report to console
     */
    static displayReport(report: AnalysisReport): void;
}
//# sourceMappingURL=report-generator.d.ts.map