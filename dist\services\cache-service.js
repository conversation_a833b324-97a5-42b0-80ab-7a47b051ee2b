"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cacheService = exports.CacheService = void 0;
const node_cache_1 = __importDefault(require("node-cache"));
const settings_1 = require("@/config/settings");
const logger_1 = require("@/utils/logger");
class CacheService {
    constructor(options) {
        this.hitCount = 0;
        this.missCount = 0;
        this.cache = new node_cache_1.default({
            stdTTL: 600, // Default 10 minutes
            checkperiod: 120, // Check for expired keys every 2 minutes
            useClones: false, // Don't clone objects for better performance
            ...options,
        });
        // Log cache events
        this.cache.on('set', (key, value) => {
            logger_1.Logger.cacheOperation('set', key);
        });
        this.cache.on('expired', (key, value) => {
            logger_1.Logger.debug('Cache key expired', { key });
        });
        this.cache.on('del', (key, value) => {
            logger_1.Logger.debug('Cache key deleted', { key });
        });
        logger_1.Logger.info('Cache service initialized');
    }
    /**
     * Get TTL for specific cache type
     */
    getTTL(cacheType) {
        switch (cacheType) {
            case 'matches':
                return settings_1.config.cache.ttl.matches / 1000; // Convert to seconds
            case 'teams':
                return settings_1.config.cache.ttl.teams / 1000;
            case 'players':
                return settings_1.config.cache.ttl.players / 1000;
            case 'rankings':
                return settings_1.config.cache.ttl.rankings / 1000;
            case 'team_stats':
                return settings_1.config.cache.ttl.teams / 1000;
            case 'player_stats':
                return settings_1.config.cache.ttl.players / 1000;
            case 'match_details':
                return settings_1.config.cache.ttl.matches / 1000;
            case 'events':
                return settings_1.config.cache.ttl.teams / 1000;
            default:
                return 600; // Default 10 minutes
        }
    }
    /**
     * Generate cache key
     */
    generateKey(cacheType, identifier, params) {
        let key = `${cacheType}:${identifier}`;
        if (params) {
            const sortedParams = Object.keys(params)
                .sort()
                .map(k => `${k}=${JSON.stringify(params[k])}`)
                .join('&');
            if (sortedParams) {
                key += `:${sortedParams}`;
            }
        }
        return key;
    }
    /**
     * Get value from cache
     */
    get(cacheType, identifier, params) {
        const key = this.generateKey(cacheType, identifier, params);
        const value = this.cache.get(key);
        if (value !== undefined) {
            this.hitCount++;
            logger_1.Logger.cacheOperation('hit', key);
            return value;
        }
        else {
            this.missCount++;
            logger_1.Logger.cacheOperation('miss', key);
            return undefined;
        }
    }
    /**
     * Set value in cache
     */
    set(cacheType, identifier, value, params, customTTL) {
        const key = this.generateKey(cacheType, identifier, params);
        const ttl = customTTL || this.getTTL(cacheType);
        const success = this.cache.set(key, value, ttl);
        if (success) {
            logger_1.Logger.debug('Cache set successful', {
                key,
                ttl,
                size: JSON.stringify(value).length,
            });
        }
        else {
            logger_1.Logger.warn('Cache set failed', { key });
        }
        return success;
    }
    /**
     * Delete value from cache
     */
    delete(cacheType, identifier, params) {
        const key = this.generateKey(cacheType, identifier, params);
        return this.cache.del(key);
    }
    /**
     * Check if key exists in cache
     */
    has(cacheType, identifier, params) {
        const key = this.generateKey(cacheType, identifier, params);
        return this.cache.has(key);
    }
    /**
     * Get or set pattern - fetch from cache or execute function and cache result
     */
    async getOrSet(cacheType, identifier, fetchFunction, params, customTTL) {
        // Try to get from cache first
        const cached = this.get(cacheType, identifier, params);
        if (cached !== undefined) {
            return cached;
        }
        // Not in cache, fetch the data
        logger_1.Logger.debug('Cache miss - fetching data', {
            cacheType,
            identifier,
            params,
        });
        try {
            const data = await fetchFunction();
            // Cache the result
            this.set(cacheType, identifier, data, params, customTTL);
            return data;
        }
        catch (error) {
            logger_1.Logger.error('Failed to fetch data for cache', error, {
                cacheType,
                identifier,
                params,
            });
            throw error;
        }
    }
    /**
     * Clear all cache entries of a specific type
     */
    clearType(cacheType) {
        const keys = this.cache.keys();
        const typeKeys = keys.filter(key => key.startsWith(`${cacheType}:`));
        if (typeKeys.length > 0) {
            this.cache.del(typeKeys);
            logger_1.Logger.info('Cache type cleared', {
                cacheType,
                keysCleared: typeKeys.length,
            });
        }
    }
    /**
     * Clear all cache entries
     */
    clearAll() {
        this.cache.flushAll();
        this.hitCount = 0;
        this.missCount = 0;
        logger_1.Logger.info('All cache cleared');
    }
    /**
     * Get cache statistics
     */
    getStats() {
        const keys = this.cache.keys().length;
        const total = this.hitCount + this.missCount;
        const hitRate = total > 0 ? (this.hitCount / total) * 100 : 0;
        return {
            keys,
            hits: this.hitCount,
            misses: this.missCount,
            hitRate: Math.round(hitRate * 100) / 100,
            size: this.cache.getStats().vsize || 0,
        };
    }
    /**
     * Get cache keys by pattern
     */
    getKeysByPattern(pattern) {
        const keys = this.cache.keys();
        return keys.filter(key => key.includes(pattern));
    }
    /**
     * Get TTL for a specific key
     */
    getTTLForKey(cacheType, identifier, params) {
        const key = this.generateKey(cacheType, identifier, params);
        return this.cache.getTtl(key) || 0;
    }
    /**
     * Extend TTL for a specific key
     */
    extendTTL(cacheType, identifier, additionalSeconds, params) {
        const key = this.generateKey(cacheType, identifier, params);
        const currentTTL = this.cache.getTtl(key);
        if (currentTTL && currentTTL > 0) {
            const newTTL = Math.floor((currentTTL - Date.now()) / 1000) + additionalSeconds;
            return this.cache.ttl(key, newTTL);
        }
        return false;
    }
    /**
     * Warm up cache with commonly accessed data
     */
    async warmUp(warmUpFunctions) {
        logger_1.Logger.info('Starting cache warm-up', {
            functions: warmUpFunctions.length,
        });
        const startTime = Date.now();
        try {
            await Promise.all(warmUpFunctions.map(fn => fn()));
            const duration = Date.now() - startTime;
            logger_1.Logger.info('Cache warm-up completed', {
                duration,
                cacheStats: this.getStats(),
            });
        }
        catch (error) {
            logger_1.Logger.error('Cache warm-up failed', error);
            throw error;
        }
    }
}
exports.CacheService = CacheService;
// Export singleton instance
exports.cacheService = new CacheService();
//# sourceMappingURL=cache-service.js.map