export type DateMode = 'today' | 'tomorrow' | 'custom';
export declare class DateUtils {
    /**
     * Get date based on mode
     */
    static getAnalysisDate(mode: DateMode, customDate?: string): Date;
    /**
     * Get date range for analysis (start and end of day)
     */
    static getDateRange(date: Date): {
        start: Date;
        end: Date;
    };
    /**
     * Format date for HLTV API calls
     */
    static formatForHLTV(date: Date): string;
    /**
     * Format date for display
     */
    static formatForDisplay(date: Date): string;
    /**
     * Format date for UTC display
     */
    static formatForUTC(date: Date): string;
    /**
     * Check if date is within last N days
     */
    static isWithinLastDays(date: Date, days: number): boolean;
    /**
     * Check if date is within last N months
     */
    static isWithinLastMonths(date: Date, months: number): boolean;
    /**
     * Calculate days between two dates
     */
    static daysBetween(date1: Date, date2: Date): number;
    /**
     * Get relative time string (e.g., "2 hours ago", "in 3 days")
     */
    static getRelativeTime(date: Date): string;
    /**
     * Check if it's a reasonable time for analysis (not too far in future)
     */
    static isReasonableAnalysisDate(date: Date): boolean;
    /**
     * Parse various date string formats
     */
    static parseFlexibleDate(dateString: string): Date;
}
//# sourceMappingURL=date-utils.d.ts.map