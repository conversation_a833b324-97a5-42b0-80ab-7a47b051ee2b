version: '3.8'

services:
  hltv-api:
    build:
      context: ./hltv-api
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - RATE_LIMITING_ENABLE=false
      - RATE_LIMITING_FREQUENCY=2/3seconds
    volumes:
      - ./hltv-api:/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/docs"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  cs2-betting-analytics:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - HLTV_API_BASE_URL=http://hltv-api:8000
      - LOG_LEVEL=info
    depends_on:
      - hltv-api
    volumes:
      - ./reports:/app/reports
      - ./logs:/app/logs
    restart: unless-stopped

networks:
  default:
    name: cs2-betting-network
