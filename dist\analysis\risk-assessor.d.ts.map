{"version": 3, "file": "risk-assessor.d.ts", "sourceRoot": "", "sources": ["../../src/analysis/risk-assessor.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAgB,MAAM,kBAAkB,CAAC;AAC3D,OAAO,EAAE,kBAAkB,EAAE,MAAM,iBAAiB,CAAC;AAKrD,MAAM,WAAW,oBAAoB;IACnC,SAAS,EAAE,SAAS,CAAC;IACrB,KAAK,EAAE,MAAM,CAAC;IAEd,QAAQ,EAAE;QACR,UAAU,EAAE;YACV,KAAK,EAAE,MAAM,CAAC;YACd,KAAK,EAAE,OAAO,CAAC;YACf,MAAM,EAAE,MAAM,CAAC;SAChB,CAAC;QACF,kBAAkB,EAAE;YAClB,KAAK,EAAE,MAAM,CAAC;YACd,KAAK,EAAE,OAAO,CAAC;YACf,MAAM,EAAE,MAAM,CAAC;SAChB,CAAC;QACF,eAAe,EAAE;YACf,KAAK,EAAE,MAAM,CAAC;YACd,KAAK,EAAE,OAAO,CAAC;YACf,MAAM,EAAE,MAAM,CAAC;SAChB,CAAC;QACF,WAAW,EAAE;YACX,KAAK,EAAE,MAAM,CAAC;YACd,KAAK,EAAE,OAAO,CAAC;YACf,MAAM,EAAE,MAAM,CAAC;SAChB,CAAC;QACF,gBAAgB,EAAE;YAChB,KAAK,EAAE,MAAM,CAAC;YACd,KAAK,EAAE,OAAO,CAAC;YACf,MAAM,EAAE,MAAM,CAAC;SAChB,CAAC;QACF,UAAU,EAAE;YACV,KAAK,EAAE,MAAM,CAAC;YACd,KAAK,EAAE,OAAO,CAAC;YACf,MAAM,EAAE,MAAM,CAAC;SAChB,CAAC;QACF,aAAa,EAAE;YACb,KAAK,EAAE,MAAM,CAAC;YACd,KAAK,EAAE,OAAO,CAAC;YACf,MAAM,EAAE,MAAM,CAAC;SAChB,CAAC;KACH,CAAC;IAEF,gBAAgB,EAAE,KAAK,CAAC;QACtB,MAAM,EAAE,MAAM,CAAC;QACf,MAAM,EAAE,MAAM,CAAC;QACf,QAAQ,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC;KACrC,CAAC,CAAC;IAEH,iBAAiB,EAAE,MAAM,EAAE,CAAC;IAC5B,WAAW,EAAE,MAAM,EAAE,CAAC;IAEtB,cAAc,EAAE,SAAS,GAAG,SAAS,GAAG,OAAO,CAAC;IAChD,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,gBAAgB;IAC/B,aAAa,EAAE,kBAAkB,CAAC;IAClC,aAAa,EAAE,kBAAkB,CAAC;IAClC,WAAW,EAAE,MAAM,CAAC;IACpB,cAAc,EAAE,MAAM,CAAC;IACvB,YAAY,EAAE,MAAM,CAAC;CACtB;AAED,qBAAa,YAAY;IACvB,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAQtC;IAEF;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,gBAAgB,GAAG,oBAAoB;IAsElE;;OAEG;IACH,OAAO,CAAC,MAAM,CAAC,gBAAgB;IAoD/B;;OAEG;IACH,OAAO,CAAC,MAAM,CAAC,qBAAqB;IAuDpC;;OAEG;IACH,OAAO,CAAC,MAAM,CAAC,kBAAkB;IAajC;;OAEG;IACH,OAAO,CAAC,MAAM,CAAC,kBAAkB;IA6CjC;;OAEG;IACH,OAAO,CAAC,MAAM,CAAC,yBAAyB;IAkCxC;;OAEG;IACH,OAAO,CAAC,MAAM,CAAC,mBAAmB;IAkClC;;OAEG;IACH,OAAO,CAAC,MAAM,CAAC,sBAAsB;IAkBrC;;OAEG;IACH,OAAO,CAAC,MAAM,CAAC,6BAA6B;IAgB5C;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,OAAO,EAAE,gBAAgB,GAAG,OAAO;CAchE"}