export interface PlayerStats {
    playerId: number;
    name: string;
    nickname: string;
    team: {
        id: number;
        name: string;
    };
    rating: number;
    kpr: number;
    dpr: number;
    adr: number;
    kast: number;
    impact: number;
    role: 'entry' | 'support' | 'awp' | 'igl' | 'lurker';
    rolePerformance: {
        entryFragging?: {
            entryKills: number;
            entryAttempts: number;
            entrySuccessRate: number;
            openingKillRatio: number;
        };
        awpImpact?: {
            awpKills: number;
            awpRounds: number;
            awpKillsPerRound: number;
            awpRating: number;
        };
        supportValue?: {
            flashAssists: number;
            utilityDamage: number;
            tradeKills: number;
            teamworkRating: number;
        };
        leadership?: {
            roundsAsCaller: number;
            stratSuccessRate: number;
            teamCoordination: number;
        };
    };
    form: {
        last5Matches: {
            rating: number;
            kpr: number;
            adr: number;
            consistency: number;
        };
        last10Matches: {
            rating: number;
            kpr: number;
            adr: number;
            consistency: number;
        };
        trend: 'improving' | 'declining' | 'stable';
        trendStrength: number;
    };
    situational: {
        clutchPerformance: {
            clutch1v1: {
                attempts: number;
                successes: number;
                rate: number;
            };
            clutch1v2: {
                attempts: number;
                successes: number;
                rate: number;
            };
            clutch1v3: {
                attempts: number;
                successes: number;
                rate: number;
            };
            clutch1v4: {
                attempts: number;
                successes: number;
                rate: number;
            };
            clutch1v5: {
                attempts: number;
                successes: number;
                rate: number;
            };
        };
        pressurePerformance: {
            playoffRating: number;
            groupStageRating: number;
            bigMatchRating: number;
            differential: number;
        };
        mapPreferences: Array<{
            map: string;
            rating: number;
            kpr: number;
            comfort: number;
        }>;
    };
    condition: {
        age: number;
        experience: number;
        recentInjuries?: string[];
        fatigueLevel?: number;
        confidence: number;
    };
    dataQuality: {
        sampleSize: number;
        lastUpdated: Date;
        reliability: 'high' | 'medium' | 'low';
        completeness: number;
    };
}
export interface PlayerComparison {
    player1: PlayerStats;
    player2: PlayerStats;
    advantages: {
        player1: string[];
        player2: string[];
    };
    overallEdge: 'player1' | 'player2' | 'even';
    confidence: number;
}
//# sourceMappingURL=player.d.ts.map