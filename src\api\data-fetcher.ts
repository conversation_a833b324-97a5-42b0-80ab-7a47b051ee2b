import { HLTVClient } from './hltv-client';
import { cacheService } from '@/services/cache-service';
import { Logger } from '@/utils/logger';
import { DateUtils } from '@/utils/date-utils';
import { Match } from '@/models/match';
import { TeamStats } from '@/models/team';
import { PlayerStats } from '@/models/player';

export interface DataFetcherOptions {
  useCache?: boolean;
  forceRefresh?: boolean;
  maxConcurrentRequests?: number;
}

export class DataFetcher {
  private hltvClient: HLTVClient;

  constructor() {
    this.hltvClient = new HLTVClient();
  }

  /**
   * Fetch all matches for a specific date
   */
  async fetchMatches(date: Date, options: DataFetcherOptions = {}): Promise<any[]> {
    const { useCache = true, forceRefresh = false } = options;
    const dateStr = DateUtils.formatForHLTV(date);

    Logger.info('Fetching matches', { date: dateStr, useCache, forceRefresh });

    if (useCache && !forceRefresh) {
      return cacheService.getOrSet(
        'matches',
        dateStr,
        () => this.hltvClient.getMatches(date)
      );
    }

    const matches = await this.hltvClient.getMatches(date);

    if (useCache) {
      cacheService.set('matches', dateStr, matches);
    }

    Logger.info('Matches fetched', {
      date: dateStr,
      count: matches.length,
    });

    return matches;
  }

  /**
   * Fetch detailed match information
   */
  async fetchMatchDetails(matchId: number, options: DataFetcherOptions = {}): Promise<any> {
    const { useCache = true, forceRefresh = false } = options;

    Logger.debug('Fetching match details', { matchId, useCache, forceRefresh });

    if (useCache && !forceRefresh) {
      return cacheService.getOrSet(
        'match_details',
        matchId,
        () => this.hltvClient.getMatch(matchId)
      );
    }

    const matchDetails = await this.hltvClient.getMatch(matchId);

    if (useCache) {
      cacheService.set('match_details', matchId, matchDetails);
    }

    return matchDetails;
  }

  /**
   * Fetch team rankings
   */
  async fetchTeamRankings(options: DataFetcherOptions = {}): Promise<any[]> {
    const { useCache = true, forceRefresh = false } = options;

    Logger.info('Fetching team rankings', { useCache, forceRefresh });

    if (useCache && !forceRefresh) {
      return cacheService.getOrSet(
        'rankings',
        'teams',
        () => this.hltvClient.getTeamRanking()
      );
    }

    const rankings = await this.hltvClient.getTeamRanking();

    if (useCache) {
      cacheService.set('rankings', 'teams', rankings);
    }

    Logger.info('Team rankings fetched', { count: rankings.length });

    return rankings;
  }

  /**
   * Fetch team information and statistics
   */
  async fetchTeamData(teamId: number, options: DataFetcherOptions = {}): Promise<{
    info: any;
    stats: any;
  }> {
    const { useCache = true, forceRefresh = false } = options;

    Logger.debug('Fetching team data', { teamId, useCache, forceRefresh });

    // Fetch team info and stats in parallel
    const [info, stats] = await Promise.all([
      useCache && !forceRefresh
        ? cacheService.getOrSet('teams', teamId, () => this.hltvClient.getTeam(teamId))
        : this.hltvClient.getTeam(teamId),

      useCache && !forceRefresh
        ? cacheService.getOrSet('team_stats', teamId, () => this.hltvClient.getTeamStats(teamId))
        : this.hltvClient.getTeamStats(teamId),
    ]);

    if (useCache && forceRefresh) {
      cacheService.set('teams', teamId, info);
      cacheService.set('team_stats', teamId, stats);
    }

    return { info, stats };
  }

  /**
   * Fetch player information and statistics
   */
  async fetchPlayerData(playerId: number, options: DataFetcherOptions = {}): Promise<{
    info: any;
    stats: any;
  }> {
    const { useCache = true, forceRefresh = false } = options;

    Logger.debug('Fetching player data', { playerId, useCache, forceRefresh });

    // Fetch player info and stats in parallel
    const [info, stats] = await Promise.all([
      useCache && !forceRefresh
        ? cacheService.getOrSet('players', playerId, () => this.hltvClient.getPlayer(playerId))
        : this.hltvClient.getPlayer(playerId),

      useCache && !forceRefresh
        ? cacheService.getOrSet('player_stats', playerId, () => this.hltvClient.getPlayerStats(playerId))
        : this.hltvClient.getPlayerStats(playerId),
    ]);

    if (useCache && forceRefresh) {
      cacheService.set('players', playerId, info);
      cacheService.set('player_stats', playerId, stats);
    }

    return { info, stats };
  }

  /**
   * Fetch recent match results for analysis
   */
  async fetchRecentResults(options: {
    teamIds?: number[];
    startDate?: Date;
    endDate?: Date;
    limit?: number;
  } & DataFetcherOptions = {}): Promise<any[]> {
    const { useCache = true, forceRefresh = false, teamIds, startDate, endDate, limit = 50 } = options;

    const params: any = {};
    if (teamIds) params.teamIds = teamIds;
    if (startDate) params.startDate = DateUtils.formatForHLTV(startDate);
    if (endDate) params.endDate = DateUtils.formatForHLTV(endDate);

    Logger.info('Fetching recent results', { params, useCache, forceRefresh });

    const cacheKey = `recent_results_${JSON.stringify(params)}`;

    if (useCache && !forceRefresh) {
      return cacheService.getOrSet(
        'matches',
        cacheKey,
        () => this.hltvClient.getResults(params)
      );
    }

    const results = await this.hltvClient.getResults(params);

    if (useCache) {
      cacheService.set('matches', cacheKey, results);
    }

    Logger.info('Recent results fetched', { count: results.length });

    return results.slice(0, limit);
  }

  /**
   * Fetch comprehensive data for a match analysis
   */
  async fetchMatchAnalysisData(matchId: number, options: DataFetcherOptions = {}): Promise<{
    match: any;
    team1Data: { info: any; stats: any };
    team2Data: { info: any; stats: any };
    rankings: any[];
    recentResults: any[];
  }> {
    Logger.info('Fetching comprehensive match analysis data', { matchId });

    const startTime = Date.now();

    // First, get the match details to identify teams
    const match = await this.fetchMatchDetails(matchId, options);

    if (!match.team1?.id || !match.team2?.id) {
      throw new Error(`Invalid match data: missing team information for match ${matchId}`);
    }

    // Fetch all required data in parallel
    const [team1Data, team2Data, rankings, recentResults] = await Promise.all([
      this.fetchTeamData(match.team1.id, options),
      this.fetchTeamData(match.team2.id, options),
      this.fetchTeamRankings(options),
      this.fetchRecentResults({
        teamIds: [match.team1.id, match.team2.id],
        startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // Last 90 days
        endDate: new Date(),
        limit: 20,
        ...options,
      }),
    ]);

    const duration = Date.now() - startTime;

    Logger.performance('match_analysis_data_fetch', duration, {
      matchId,
      team1: match.team1.name,
      team2: match.team2.name,
    });

    return {
      match,
      team1Data,
      team2Data,
      rankings,
      recentResults,
    };
  }

  /**
   * Fetch player rankings
   */
  async fetchPlayerRankings(options: DataFetcherOptions = {}): Promise<any[]> {
    const { useCache = true, forceRefresh = false } = options;

    Logger.info('Fetching player rankings', { useCache, forceRefresh });

    if (useCache && !forceRefresh) {
      return cacheService.getOrSet(
        'rankings',
        'players',
        () => this.hltvClient.getPlayerRanking()
      );
    }

    const rankings = await this.hltvClient.getPlayerRanking();

    if (useCache) {
      cacheService.set('rankings', 'players', rankings);
    }

    Logger.info('Player rankings fetched', { count: rankings.length });

    return rankings;
  }

  /**
   * Batch fetch multiple teams data
   */
  async fetchMultipleTeamsData(
    teamIds: number[],
    options: DataFetcherOptions = {}
  ): Promise<Array<{ teamId: number; info: any; stats: any }>> {
    const { maxConcurrentRequests = 3 } = options;

    Logger.info('Batch fetching teams data', {
      teamIds,
      count: teamIds.length,
      maxConcurrent: maxConcurrentRequests,
    });

    const results: Array<{ teamId: number; info: any; stats: any }> = [];

    // Process teams in batches to respect rate limits
    for (let i = 0; i < teamIds.length; i += maxConcurrentRequests) {
      const batch = teamIds.slice(i, i + maxConcurrentRequests);

      const batchResults = await Promise.all(
        batch.map(async (teamId) => {
          try {
            const data = await this.fetchTeamData(teamId, options);
            return { teamId, ...data };
          } catch (error) {
            Logger.error(`Failed to fetch data for team ${teamId}`, error as Error);
            return { teamId, info: null, stats: null };
          }
        })
      );

      results.push(...batchResults);

      // Small delay between batches
      if (i + maxConcurrentRequests < teamIds.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    Logger.info('Batch team data fetch completed', {
      requested: teamIds.length,
      successful: results.filter(r => r.info && r.stats).length,
    });

    return results;
  }

  /**
   * Get client statistics
   */
  getClientStats(): { requests: number; lastRequestTime: number; cacheStats: any } {
    const hltvStats = this.hltvClient.getRequestStats();
    return {
      requests: hltvStats.count,
      lastRequestTime: hltvStats.lastRequestTime,
      cacheStats: cacheService.getStats(),
    };
  }
}
