"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TeamAnalyzer = void 0;
const math_utils_1 = require("@/utils/math-utils");
const date_utils_1 = require("@/utils/date-utils");
const logger_1 = require("@/utils/logger");
const settings_1 = require("@/config/settings");
class TeamAnalyzer {
    /**
     * Analyze team performance and characteristics
     */
    static analyzeTeam(teamData, teamStats, rankings) {
        logger_1.Logger.debug('Analyzing team', { teamId: teamData.id, name: teamData.name });
        const startTime = Date.now();
        try {
            const analysis = {
                teamId: teamData.id,
                name: teamData.name,
                ranking: this.extractRanking(teamData.id, rankings),
                overallRating: this.calculateOverallRating(teamStats),
                form: this.analyzeForm(teamStats),
                performance: this.analyzePerformance(teamStats),
                mapPool: this.analyzeMapPool(teamStats),
                roster: this.analyzeRoster(teamData, teamStats),
                contextualFactors: this.analyzeContextualFactors(teamStats),
                strengths: [],
                weaknesses: [],
                dataQuality: this.assessDataQuality(teamStats),
            };
            // Identify strengths and weaknesses
            analysis.strengths = this.identifyStrengths(analysis);
            analysis.weaknesses = this.identifyWeaknesses(analysis);
            const duration = Date.now() - startTime;
            logger_1.Logger.performance('team_analysis', duration, {
                teamId: analysis.teamId,
                name: analysis.name,
            });
            return analysis;
        }
        catch (error) {
            logger_1.Logger.error('Team analysis failed', error, {
                teamId: teamData.id,
                name: teamData.name,
            });
            throw error;
        }
    }
    /**
     * Extract team ranking from rankings data
     */
    static extractRanking(teamId, rankings) {
        const teamRanking = rankings.find(r => r.team?.id === teamId);
        return teamRanking?.place || 999;
    }
    /**
     * Calculate overall team rating
     */
    static calculateOverallRating(teamStats) {
        if (!teamStats)
            return 0;
        // Use multiple factors to calculate overall rating
        const factors = [
            teamStats.rating || 1.0,
            (teamStats.wins / Math.max(teamStats.wins + teamStats.losses, 1)) * 2, // Win rate scaled
            (teamStats.mapsWon / Math.max(teamStats.mapsPlayed, 1)) * 2, // Map win rate scaled
        ];
        return math_utils_1.MathUtils.weightedAverage(factors, [0.5, 0.3, 0.2]);
    }
    /**
     * Analyze team form and recent performance
     */
    static analyzeForm(teamStats) {
        const recentMatches = teamStats.recentResults || [];
        const totalMatches = teamStats.wins + teamStats.losses;
        // Recent form (last 10 matches)
        const recentWins = recentMatches.filter((m) => m.result === 'win').length;
        const recentTotal = recentMatches.length;
        const recentWinRate = recentTotal > 0 ? (recentWins / recentTotal) * 100 : 0;
        // Overall form
        const overallWinRate = totalMatches > 0 ? (teamStats.wins / totalMatches) * 100 : 0;
        // Trend analysis
        const trend = this.analyzeTrend(recentMatches);
        // Consistency (coefficient of variation of recent performance)
        const recentRatings = recentMatches.map((m) => m.rating || 1.0);
        const consistency = 100 - math_utils_1.MathUtils.coefficientOfVariation(recentRatings);
        return {
            recent: {
                winRate: math_utils_1.MathUtils.round(recentWinRate, 1),
                matches: recentTotal,
                trend,
                consistency: math_utils_1.MathUtils.round(Math.max(0, consistency), 1),
            },
            overall: {
                winRate: math_utils_1.MathUtils.round(overallWinRate, 1),
                totalMatches,
            },
        };
    }
    /**
     * Analyze performance trends
     */
    static analyzeTrend(recentMatches) {
        if (recentMatches.length < 5)
            return 'stable';
        const ratings = recentMatches.slice(-5).map((m) => m.rating || 1.0);
        const firstHalf = ratings.slice(0, Math.floor(ratings.length / 2));
        const secondHalf = ratings.slice(Math.floor(ratings.length / 2));
        const firstAvg = firstHalf.reduce((sum, r) => sum + r, 0) / firstHalf.length;
        const secondAvg = secondHalf.reduce((sum, r) => sum + r, 0) / secondHalf.length;
        const difference = secondAvg - firstAvg;
        if (difference > 0.05)
            return 'improving';
        if (difference < -0.05)
            return 'declining';
        return 'stable';
    }
    /**
     * Analyze team performance metrics
     */
    static analyzePerformance(teamStats) {
        return {
            openingKills: teamStats.openingKillRating || 0,
            tradeKillEfficiency: teamStats.tradeKillRating || 0,
            economyManagement: teamStats.economyRating || 0,
            clutchPerformance: teamStats.clutchRating || 0,
            pistolRounds: teamStats.pistolRoundRating || 0,
            mapControl: teamStats.mapControlRating || 0,
        };
    }
    /**
     * Analyze team map pool
     */
    static analyzeMapPool(teamStats) {
        const mapStats = teamStats.mapStats || [];
        const strongMaps = mapStats
            .filter((m) => m.winRate >= 65 && m.played >= 3)
            .map((m) => ({
            map: m.name,
            winRate: m.winRate,
            confidence: this.calculateMapConfidence(m.played, m.winRate),
        }))
            .sort((a, b) => b.winRate - a.winRate);
        const weakMaps = mapStats
            .filter((m) => m.winRate <= 40 && m.played >= 3)
            .map((m) => ({
            map: m.name,
            winRate: m.winRate,
            confidence: this.calculateMapConfidence(m.played, m.winRate),
        }))
            .sort((a, b) => a.winRate - b.winRate);
        const overallAdvantage = this.calculateMapPoolAdvantage(mapStats);
        return {
            strongMaps,
            weakMaps,
            overallAdvantage,
        };
    }
    /**
     * Calculate map confidence based on sample size and win rate
     */
    static calculateMapConfidence(played, winRate) {
        // Higher confidence with more games played and extreme win rates
        const sampleConfidence = Math.min(played / 10, 1) * 50;
        const winRateConfidence = Math.abs(winRate - 50);
        return math_utils_1.MathUtils.round(sampleConfidence + winRateConfidence, 1);
    }
    /**
     * Calculate overall map pool advantage
     */
    static calculateMapPoolAdvantage(mapStats) {
        if (!mapStats.length)
            return 0;
        const weightedWinRate = math_utils_1.MathUtils.weightedAverage(mapStats.map(m => m.winRate), mapStats.map(m => m.played));
        return math_utils_1.MathUtils.round(weightedWinRate - 50, 1); // Advantage over 50% baseline
    }
    /**
     * Analyze roster characteristics
     */
    static analyzeRoster(teamData, teamStats) {
        const players = teamData.players || [];
        const stability = this.calculateRosterStability(players);
        const experience = this.calculateAverageExperience(players);
        const averageRating = this.calculateAverageRating(players);
        const keyPlayers = this.identifyKeyPlayers(players);
        return {
            stability,
            experience,
            averageRating,
            keyPlayers,
        };
    }
    /**
     * Calculate roster stability
     */
    static calculateRosterStability(players) {
        if (!players.length)
            return 0;
        const now = new Date();
        const stableThreshold = 90; // 90 days
        const stablePlayers = players.filter(p => {
            if (!p.joinDate)
                return true; // Assume stable if no join date
            return date_utils_1.DateUtils.daysBetween(new Date(p.joinDate), now) >= stableThreshold;
        });
        return math_utils_1.MathUtils.round((stablePlayers.length / players.length) * 100, 1);
    }
    /**
     * Calculate average team experience
     */
    static calculateAverageExperience(players) {
        if (!players.length)
            return 0;
        const experiences = players.map(p => p.experience || 0);
        return math_utils_1.MathUtils.round(experiences.reduce((sum, exp) => sum + exp, 0) / experiences.length, 1);
    }
    /**
     * Calculate average team rating
     */
    static calculateAverageRating(players) {
        if (!players.length)
            return 1.0;
        const ratings = players.map(p => p.rating || 1.0);
        return math_utils_1.MathUtils.round(ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length, 2);
    }
    /**
     * Identify key players
     */
    static identifyKeyPlayers(players) {
        return players
            .filter(p => p.rating >= 1.05) // Above average players
            .map(p => ({
            name: p.name,
            role: p.role || 'unknown',
            rating: p.rating || 1.0,
            impact: this.calculatePlayerImpact(p),
        }))
            .sort((a, b) => b.impact - a.impact)
            .slice(0, 3); // Top 3 key players
    }
    /**
     * Calculate player impact score
     */
    static calculatePlayerImpact(player) {
        const factors = [
            player.rating || 1.0,
            player.kpr || 0.7,
            player.adr || 70,
            player.kast || 70,
        ];
        const weights = [0.4, 0.2, 0.2, 0.2];
        return math_utils_1.MathUtils.round(math_utils_1.MathUtils.weightedAverage(factors, weights), 2);
    }
    /**
     * Analyze contextual factors
     */
    static analyzeContextualFactors(teamStats) {
        return {
            patchAdaptation: teamStats.patchAdaptationRating || 50,
            motivationLevel: teamStats.motivationRating || 50,
            travelImpact: teamStats.travelImpactRating || 0,
            pressureHandling: teamStats.pressureRating || 50,
        };
    }
    /**
     * Assess data quality
     */
    static assessDataQuality(teamStats) {
        const sampleSize = (teamStats.wins || 0) + (teamStats.losses || 0);
        const completeness = this.calculateDataCompleteness(teamStats);
        let reliability = 'low';
        if (sampleSize >= 20 && completeness >= 80)
            reliability = 'high';
        else if (sampleSize >= 10 && completeness >= 60)
            reliability = 'medium';
        return {
            completeness,
            reliability,
            sampleSize,
            lastUpdated: new Date(),
        };
    }
    /**
     * Calculate data completeness percentage
     */
    static calculateDataCompleteness(teamStats) {
        const requiredFields = [
            'wins', 'losses', 'rating', 'mapStats', 'recentResults',
            'openingKillRating', 'economyRating', 'clutchRating'
        ];
        const presentFields = requiredFields.filter(field => teamStats[field] !== undefined && teamStats[field] !== null);
        return math_utils_1.MathUtils.round((presentFields.length / requiredFields.length) * 100, 1);
    }
    /**
     * Identify team strengths
     */
    static identifyStrengths(analysis) {
        const strengths = [];
        if (analysis.form.recent.winRate >= 70) {
            strengths.push('Excellent recent form');
        }
        if (analysis.performance.openingKills >= settings_1.config.metrics.minOpeningKillRate) {
            strengths.push('Strong opening kill success');
        }
        if (analysis.performance.clutchPerformance >= settings_1.config.metrics.minClutchSuccessRate) {
            strengths.push('Reliable clutch performance');
        }
        if (analysis.mapPool.strongMaps.length >= 3) {
            strengths.push('Deep map pool');
        }
        if (analysis.roster.stability >= 90) {
            strengths.push('Stable roster');
        }
        if (analysis.roster.averageRating >= 1.10) {
            strengths.push('High individual skill level');
        }
        return strengths;
    }
    /**
     * Identify team weaknesses
     */
    static identifyWeaknesses(analysis) {
        const weaknesses = [];
        if (analysis.form.recent.winRate <= 40) {
            weaknesses.push('Poor recent form');
        }
        if (analysis.performance.economyManagement <= 40) {
            weaknesses.push('Weak economy management');
        }
        if (analysis.mapPool.weakMaps.length >= 3) {
            weaknesses.push('Limited map pool');
        }
        if (analysis.roster.stability <= 60) {
            weaknesses.push('Roster instability');
        }
        if (analysis.dataQuality.reliability === 'low') {
            weaknesses.push('Insufficient data for reliable analysis');
        }
        return weaknesses;
    }
}
exports.TeamAnalyzer = TeamAnalyzer;
//# sourceMappingURL=team-analyzer.js.map