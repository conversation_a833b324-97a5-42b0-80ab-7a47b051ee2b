import { RiskLevel } from '@/models/betting';
import { TeamAnalysisResult } from './team-analyzer';
export interface RiskAssessmentResult {
    riskLevel: RiskLevel;
    score: number;
    criteria: {
        rankingGap: {
            value: number;
            meets: boolean;
            weight: number;
        };
        favoredTeamWinRate: {
            value: number;
            meets: boolean;
            weight: number;
        };
        rosterStability: {
            value: number;
            meets: boolean;
            weight: number;
        };
        matchFormat: {
            value: string;
            meets: boolean;
            weight: number;
        };
        mapPoolAdvantage: {
            value: number;
            meets: boolean;
            weight: number;
        };
        teamRating: {
            value: number;
            meets: boolean;
            weight: number;
        };
        pistolWinRate: {
            value: number;
            meets: boolean;
            weight: number;
        };
    };
    exclusionFactors: Array<{
        factor: string;
        reason: string;
        severity: 'low' | 'medium' | 'high';
    }>;
    mitigatingFactors: string[];
    riskFactors: string[];
    recommendation: 'proceed' | 'caution' | 'avoid';
    confidence: number;
}
export interface MatchRiskContext {
    team1Analysis: TeamAnalysisResult;
    team2Analysis: TeamAnalysisResult;
    matchFormat: string;
    tournamentTier: string;
    significance: number;
}
export declare class RiskAssessor {
    private static readonly CRITERIA_WEIGHTS;
    /**
     * Assess risk level for a betting opportunity
     */
    static assessRisk(context: MatchRiskContext): RiskAssessmentResult;
    /**
     * Evaluate all risk criteria
     */
    private static evaluateCriteria;
    /**
     * Check for exclusion factors that would disqualify the bet
     */
    private static checkExclusionFactors;
    /**
     * Calculate overall risk score
     */
    private static calculateRiskScore;
    /**
     * Determine risk level based on criteria and exclusions
     */
    private static determineRiskLevel;
    /**
     * Identify mitigating factors that reduce risk
     */
    private static identifyMitigatingFactors;
    /**
     * Identify risk factors that increase risk
     */
    private static identifyRiskFactors;
    /**
     * Generate betting recommendation
     */
    private static generateRecommendation;
    /**
     * Calculate confidence in risk assessment
     */
    private static calculateAssessmentConfidence;
    /**
     * Check if match meets minimum criteria for any betting consideration
     */
    static meetsMinimumCriteria(context: MatchRiskContext): boolean;
}
//# sourceMappingURL=risk-assessor.d.ts.map