"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logger = exports.logger = void 0;
const winston_1 = __importDefault(require("winston"));
const settings_1 = require("@/config/settings");
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
// Ensure logs directory exists
const logsDir = path_1.default.dirname(settings_1.config.logging.filePath);
if (!fs_1.default.existsSync(logsDir)) {
    fs_1.default.mkdirSync(logsDir, { recursive: true });
}
// Custom format for console output
const consoleFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston_1.default.format.colorize(), winston_1.default.format.printf(({ timestamp, level, message, ...meta }) => {
    let metaStr = '';
    if (Object.keys(meta).length > 0) {
        metaStr = ` ${JSON.stringify(meta)}`;
    }
    return `${timestamp} [${level}]: ${message}${metaStr}`;
}));
// Custom format for file output
const fileFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json());
// Create logger instance
exports.logger = winston_1.default.createLogger({
    level: settings_1.config.logging.level,
    format: fileFormat,
    defaultMeta: { service: 'cs2-betting-analytics' },
    transports: [
        // File transport
        new winston_1.default.transports.File({
            filename: settings_1.config.logging.filePath,
            maxsize: 10 * 1024 * 1024, // 10MB
            maxFiles: 5,
            tailable: true,
        }),
        // Error file transport
        new winston_1.default.transports.File({
            filename: path_1.default.join(logsDir, 'error.log'),
            level: 'error',
            maxsize: 10 * 1024 * 1024, // 10MB
            maxFiles: 5,
            tailable: true,
        }),
    ],
});
// Add console transport in development
if (process.env.NODE_ENV !== 'production') {
    exports.logger.add(new winston_1.default.transports.Console({
        format: consoleFormat,
    }));
}
// Utility functions for structured logging
class Logger {
    /**
     * Log API request
     */
    static apiRequest(endpoint, params) {
        exports.logger.info('API Request', {
            type: 'api_request',
            endpoint,
            params,
            timestamp: new Date().toISOString(),
        });
    }
    /**
     * Log API response
     */
    static apiResponse(endpoint, duration, success, error) {
        exports.logger.info('API Response', {
            type: 'api_response',
            endpoint,
            duration,
            success,
            error,
            timestamp: new Date().toISOString(),
        });
    }
    /**
     * Log analysis start
     */
    static analysisStart(date, mode) {
        exports.logger.info('Analysis Started', {
            type: 'analysis_start',
            date,
            mode,
            timestamp: new Date().toISOString(),
        });
    }
    /**
     * Log analysis completion
     */
    static analysisComplete(date, duration, matchesAnalyzed, opportunitiesFound) {
        exports.logger.info('Analysis Completed', {
            type: 'analysis_complete',
            date,
            duration,
            matchesAnalyzed,
            opportunitiesFound,
            timestamp: new Date().toISOString(),
        });
    }
    /**
     * Log betting opportunity found
     */
    static opportunityFound(matchId, teams, betType, expectedValue, confidence) {
        exports.logger.info('Betting Opportunity Found', {
            type: 'opportunity_found',
            matchId,
            teams,
            betType,
            expectedValue,
            confidence,
            timestamp: new Date().toISOString(),
        });
    }
    /**
     * Log data quality issues
     */
    static dataQualityIssue(type, description, severity) {
        exports.logger.warn('Data Quality Issue', {
            type: 'data_quality_issue',
            issueType: type,
            description,
            severity,
            timestamp: new Date().toISOString(),
        });
    }
    /**
     * Log rate limiting
     */
    static rateLimited(endpoint, retryAfter) {
        exports.logger.warn('Rate Limited', {
            type: 'rate_limited',
            endpoint,
            retryAfter,
            timestamp: new Date().toISOString(),
        });
    }
    /**
     * Log cache operations
     */
    static cacheOperation(operation, key) {
        exports.logger.debug('Cache Operation', {
            type: 'cache_operation',
            operation,
            key,
            timestamp: new Date().toISOString(),
        });
    }
    /**
     * Log performance metrics
     */
    static performance(operation, duration, details) {
        exports.logger.info('Performance Metric', {
            type: 'performance',
            operation,
            duration,
            details,
            timestamp: new Date().toISOString(),
        });
    }
    /**
     * Log error with context
     */
    static error(message, error, context) {
        exports.logger.error(message, {
            type: 'error',
            error: {
                message: error.message,
                stack: error.stack,
                name: error.name,
            },
            context,
            timestamp: new Date().toISOString(),
        });
    }
    /**
     * Log warning with context
     */
    static warn(message, context) {
        exports.logger.warn(message, {
            type: 'warning',
            context,
            timestamp: new Date().toISOString(),
        });
    }
    /**
     * Log info with context
     */
    static info(message, context) {
        exports.logger.info(message, {
            type: 'info',
            context,
            timestamp: new Date().toISOString(),
        });
    }
    /**
     * Log debug with context
     */
    static debug(message, context) {
        exports.logger.debug(message, {
            type: 'debug',
            context,
            timestamp: new Date().toISOString(),
        });
    }
}
exports.Logger = Logger;
exports.default = exports.logger;
//# sourceMappingURL=logger.js.map