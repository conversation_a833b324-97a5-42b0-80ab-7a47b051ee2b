import { BetType } from '@/models/betting';
import { TeamAnalysisResult } from './team-analyzer';
import { RiskAssessmentResult } from './risk-assessor';
import { MathUtils } from '@/utils/math-utils';
import { Logger } from '@/utils/logger';
import { config } from '@/config/settings';

export interface EVCalculationResult {
  betType: BetType;
  selection: string;
  
  odds: {
    decimal: number;
    impliedProbability: number;
    source: string;
  };
  
  assessment: {
    trueProbability: number;
    confidence: number;
    methodology: string[];
  };
  
  expectedValue: {
    absolute: number;
    percentage: number;
    isPositive: boolean;
  };
  
  kelly: {
    fraction: number;
    recommendedStake: number;
    maxStake: number;
  };
  
  factors: {
    strengths: string[];
    concerns: string[];
    keyAssumptions: string[];
  };
  
  recommendation: {
    action: 'bet' | 'pass' | 'monitor';
    reasoning: string;
    confidence: number;
  };
}

export interface MatchContext {
  team1Analysis: TeamAnalysisResult;
  team2Analysis: TeamAnalysisResult;
  riskAssessment: RiskAssessmentResult;
  matchFormat: string;
  significance: number;
  odds?: {
    team1: number;
    team2: number;
    source: string;
  };
}

export class EVCalculator {
  /**
   * Calculate expected value for match winner bet
   */
  static calculateMatchWinnerEV(
    context: MatchContext,
    favoredTeam: 'team1' | 'team2'
  ): EVCalculationResult {
    Logger.debug('Calculating match winner EV', {
      team1: context.team1Analysis.name,
      team2: context.team2Analysis.name,
      favoredTeam,
    });
    
    const favoredAnalysis = favoredTeam === 'team1' ? context.team1Analysis : context.team2Analysis;
    const underdogAnalysis = favoredTeam === 'team1' ? context.team2Analysis : context.team1Analysis;
    const odds = favoredTeam === 'team1' ? context.odds?.team1 : context.odds?.team2;
    
    if (!odds) {
      throw new Error('Odds not available for EV calculation');
    }
    
    // Calculate true probability
    const trueProbability = this.calculateMatchWinProbability(
      favoredAnalysis,
      underdogAnalysis,
      context
    );
    
    // Calculate confidence in assessment
    const confidence = this.calculateConfidence(context, 'match_winner');
    
    // Calculate expected value
    const impliedProbability = MathUtils.impliedProbability(odds);
    const expectedValue = MathUtils.expectedValue(trueProbability, odds);
    const expectedValuePercentage = MathUtils.expectedValuePercentage(trueProbability, odds);
    
    // Calculate Kelly Criterion
    const kellyFraction = MathUtils.kellyCriterion(trueProbability, odds, config.analysis.defaultStakeAmount);
    
    // Generate factors and recommendation
    const factors = this.generateMatchWinnerFactors(favoredAnalysis, underdogAnalysis, context);
    const recommendation = this.generateRecommendation(expectedValuePercentage, confidence, context.riskAssessment);
    
    return {
      betType: 'match_winner',
      selection: `${favoredAnalysis.name} to win`,
      
      odds: {
        decimal: odds,
        impliedProbability: MathUtils.round(impliedProbability, 2),
        source: context.odds?.source || 'Unknown',
      },
      
      assessment: {
        trueProbability: MathUtils.round(trueProbability, 2),
        confidence: MathUtils.round(confidence, 1),
        methodology: [
          'Team form analysis',
          'Head-to-head comparison',
          'Map pool evaluation',
          'Recent performance trends',
          'Contextual factors',
        ],
      },
      
      expectedValue: {
        absolute: MathUtils.round(expectedValue, 2),
        percentage: MathUtils.round(expectedValuePercentage, 2),
        isPositive: expectedValue > 0,
      },
      
      kelly: {
        fraction: MathUtils.round(kellyFraction / config.analysis.defaultStakeAmount, 4),
        recommendedStake: MathUtils.round(kellyFraction * 0.25, 2), // Conservative 25% of Kelly
        maxStake: MathUtils.round(kellyFraction, 2),
      },
      
      factors,
      recommendation,
    };
  }
  
  /**
   * Calculate match win probability
   */
  private static calculateMatchWinProbability(
    favoredTeam: TeamAnalysisResult,
    underdogTeam: TeamAnalysisResult,
    context: MatchContext
  ): number {
    // Base probability from ranking difference
    const rankingDiff = Math.abs(favoredTeam.ranking - underdogTeam.ranking);
    let baseProbability = 50 + Math.min(rankingDiff * 2, 30); // Max 80% from ranking alone
    
    // Adjust for recent form
    const formDifference = favoredTeam.form.recent.winRate - underdogTeam.form.recent.winRate;
    baseProbability += formDifference * 0.2; // Form impact
    
    // Adjust for overall rating difference
    const ratingDifference = favoredTeam.overallRating - underdogTeam.overallRating;
    baseProbability += ratingDifference * 10; // Rating impact
    
    // Map pool advantage
    const mapPoolAdvantage = favoredTeam.mapPool.overallAdvantage - underdogTeam.mapPool.overallAdvantage;
    baseProbability += mapPoolAdvantage * 0.3;
    
    // Format adjustment
    if (context.matchFormat.toLowerCase() === 'bo1') {
      baseProbability -= 5; // BO1 reduces favorite advantage
    } else if (context.matchFormat.toLowerCase() === 'bo5') {
      baseProbability += 3; // BO5 increases favorite advantage
    }
    
    // Consistency factor
    const consistencyDiff = favoredTeam.form.recent.consistency - underdogTeam.form.recent.consistency;
    baseProbability += consistencyDiff * 0.1;
    
    // Roster stability impact
    if (favoredTeam.roster.stability < 70) {
      baseProbability -= 3;
    }
    
    // Trend adjustment
    if (favoredTeam.form.recent.trend === 'improving') baseProbability += 2;
    if (favoredTeam.form.recent.trend === 'declining') baseProbability -= 3;
    if (underdogTeam.form.recent.trend === 'improving') baseProbability -= 2;
    if (underdogTeam.form.recent.trend === 'declining') baseProbability += 2;
    
    // Clamp to reasonable bounds
    return MathUtils.clamp(baseProbability, 55, 85);
  }
  
  /**
   * Calculate map handicap EV
   */
  static calculateMapHandicapEV(
    context: MatchContext,
    line: number,
    odds: number,
    favoredTeam: 'team1' | 'team2'
  ): EVCalculationResult {
    const favoredAnalysis = favoredTeam === 'team1' ? context.team1Analysis : context.team2Analysis;
    const underdogAnalysis = favoredTeam === 'team1' ? context.team2Analysis : context.team1Analysis;
    
    // Calculate probability of covering handicap
    const trueProbability = this.calculateHandicapProbability(
      favoredAnalysis,
      underdogAnalysis,
      line,
      context
    );
    
    const confidence = this.calculateConfidence(context, 'map_handicap');
    const impliedProbability = MathUtils.impliedProbability(odds);
    const expectedValue = MathUtils.expectedValue(trueProbability, odds);
    const expectedValuePercentage = MathUtils.expectedValuePercentage(trueProbability, odds);
    
    const kellyFraction = MathUtils.kellyCriterion(trueProbability, odds, config.analysis.defaultStakeAmount);
    
    const factors = this.generateHandicapFactors(favoredAnalysis, underdogAnalysis, line, context);
    const recommendation = this.generateRecommendation(expectedValuePercentage, confidence, context.riskAssessment);
    
    return {
      betType: 'map_handicap',
      selection: `${favoredAnalysis.name} ${line > 0 ? '+' : ''}${line} maps`,
      
      odds: {
        decimal: odds,
        impliedProbability: MathUtils.round(impliedProbability, 2),
        source: context.odds?.source || 'Unknown',
      },
      
      assessment: {
        trueProbability: MathUtils.round(trueProbability, 2),
        confidence: MathUtils.round(confidence, 1),
        methodology: [
          'Map pool analysis',
          'Historical map performance',
          'Format-specific trends',
          'Team consistency evaluation',
        ],
      },
      
      expectedValue: {
        absolute: MathUtils.round(expectedValue, 2),
        percentage: MathUtils.round(expectedValuePercentage, 2),
        isPositive: expectedValue > 0,
      },
      
      kelly: {
        fraction: MathUtils.round(kellyFraction / config.analysis.defaultStakeAmount, 4),
        recommendedStake: MathUtils.round(kellyFraction * 0.25, 2),
        maxStake: MathUtils.round(kellyFraction, 2),
      },
      
      factors,
      recommendation,
    };
  }
  
  /**
   * Calculate handicap covering probability
   */
  private static calculateHandicapProbability(
    favoredTeam: TeamAnalysisResult,
    underdogTeam: TeamAnalysisResult,
    line: number,
    context: MatchContext
  ): number {
    // Base probability from team strength difference
    const strengthDiff = favoredTeam.overallRating - underdogTeam.overallRating;
    let baseProbability = 50 + (strengthDiff * 15);
    
    // Adjust for map pool advantages
    const mapPoolDiff = favoredTeam.mapPool.overallAdvantage - underdogTeam.mapPool.overallAdvantage;
    baseProbability += mapPoolDiff * 0.5;
    
    // Format-specific adjustments
    if (context.matchFormat.toLowerCase() === 'bo3') {
      if (Math.abs(line) === 1.5) {
        baseProbability += line > 0 ? 10 : -10; // Adjust for 2-0 vs 2-1 probability
      }
    }
    
    // Consistency impact on handicap
    const consistencyDiff = favoredTeam.form.recent.consistency - underdogTeam.form.recent.consistency;
    baseProbability += consistencyDiff * 0.15;
    
    return MathUtils.clamp(baseProbability, 30, 80);
  }
  
  /**
   * Calculate confidence in EV assessment
   */
  private static calculateConfidence(context: MatchContext, betType: BetType): number {
    let baseConfidence = config.analysis.minConfidenceThreshold;
    
    // Data quality impact
    const avgDataQuality = (
      context.team1Analysis.dataQuality.completeness +
      context.team2Analysis.dataQuality.completeness
    ) / 2;
    
    baseConfidence += (avgDataQuality - 50) * 0.3;
    
    // Sample size impact
    const avgSampleSize = (
      context.team1Analysis.dataQuality.sampleSize +
      context.team2Analysis.dataQuality.sampleSize
    ) / 2;
    
    baseConfidence += Math.min(avgSampleSize / 20, 1) * 10;
    
    // Risk assessment confidence
    baseConfidence += context.riskAssessment.confidence * 0.2;
    
    // Bet type specific adjustments
    if (betType === 'match_winner') {
      baseConfidence += 5; // More confident in match winner predictions
    }
    
    return MathUtils.clamp(baseConfidence, config.analysis.minConfidenceThreshold, config.analysis.maxConfidenceThreshold);
  }
  
  /**
   * Generate factors for match winner bet
   */
  private static generateMatchWinnerFactors(
    favoredTeam: TeamAnalysisResult,
    underdogTeam: TeamAnalysisResult,
    context: MatchContext
  ): EVCalculationResult['factors'] {
    const strengths: string[] = [];
    const concerns: string[] = [];
    const keyAssumptions: string[] = [];
    
    // Strengths
    if (favoredTeam.form.recent.winRate >= 70) {
      strengths.push('Favored team in excellent form');
    }
    
    if (favoredTeam.mapPool.strongMaps.length >= 3) {
      strengths.push('Strong map pool depth');
    }
    
    if (context.riskAssessment.riskLevel === 'low') {
      strengths.push('Low risk assessment');
    }
    
    // Concerns
    if (underdogTeam.form.recent.trend === 'improving') {
      concerns.push('Underdog showing improvement');
    }
    
    if (context.matchFormat.toLowerCase() === 'bo1') {
      concerns.push('BO1 format increases variance');
    }
    
    if (favoredTeam.roster.stability < 80) {
      concerns.push('Recent roster changes');
    }
    
    // Key assumptions
    keyAssumptions.push('Current form continues');
    keyAssumptions.push('No unexpected roster changes');
    keyAssumptions.push('Standard map veto process');
    
    return { strengths, concerns, keyAssumptions };
  }
  
  /**
   * Generate factors for handicap bet
   */
  private static generateHandicapFactors(
    favoredTeam: TeamAnalysisResult,
    underdogTeam: TeamAnalysisResult,
    line: number,
    context: MatchContext
  ): EVCalculationResult['factors'] {
    const strengths: string[] = [];
    const concerns: string[] = [];
    const keyAssumptions: string[] = [];
    
    if (favoredTeam.mapPool.strongMaps.length >= Math.abs(line) + 1) {
      strengths.push('Sufficient map pool depth for handicap');
    }
    
    if (favoredTeam.form.recent.consistency >= 70) {
      strengths.push('Consistent recent performance');
    }
    
    if (underdogTeam.mapPool.weakMaps.length >= 2) {
      concerns.push('Underdog has exploitable map weaknesses');
    }
    
    keyAssumptions.push('Map veto follows expected pattern');
    keyAssumptions.push('No significant tactical surprises');
    
    return { strengths, concerns, keyAssumptions };
  }
  
  /**
   * Generate betting recommendation
   */
  private static generateRecommendation(
    expectedValuePercentage: number,
    confidence: number,
    riskAssessment: RiskAssessmentResult
  ): EVCalculationResult['recommendation'] {
    if (riskAssessment.recommendation === 'avoid') {
      return {
        action: 'pass',
        reasoning: 'Risk assessment recommends avoiding this bet',
        confidence: confidence,
      };
    }
    
    if (expectedValuePercentage < config.analysis.minPositiveEvThreshold * 100) {
      return {
        action: 'pass',
        reasoning: 'Expected value below minimum threshold',
        confidence: confidence,
      };
    }
    
    if (expectedValuePercentage >= 5 && confidence >= 75 && riskAssessment.riskLevel === 'low') {
      return {
        action: 'bet',
        reasoning: 'Strong positive EV with high confidence and low risk',
        confidence: confidence,
      };
    }
    
    if (expectedValuePercentage >= 3 && confidence >= 70) {
      return {
        action: 'bet',
        reasoning: 'Positive EV with adequate confidence',
        confidence: confidence,
      };
    }
    
    return {
      action: 'monitor',
      reasoning: 'Marginal opportunity - monitor for better odds',
      confidence: confidence,
    };
  }
}
