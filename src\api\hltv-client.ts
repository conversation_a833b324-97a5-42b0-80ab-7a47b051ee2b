import HLTV from 'hltv';
import { config } from '@/config/settings';
import { Logger } from '@/utils/logger';
import { DateUtils } from '@/utils/date-utils';

export interface HLTVRequestOptions {
  retries?: number;
  delay?: number;
}

export class HLTVClient {
  private requestCount: number = 0;
  private lastRequestTime: number = 0;

  constructor() {
    Logger.info('HLTV Client initialized', {
      requestDelay: config.hltv.requestDelay,
      maxConcurrentRequests: config.hltv.maxConcurrentRequests,
      retryAttempts: config.hltv.retryAttempts,
    });
  }

  /**
   * Rate limiting wrapper for HLTV requests
   */
  private async rateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < config.hltv.requestDelay) {
      const waitTime = config.hltv.requestDelay - timeSinceLastRequest;
      Logger.debug('Rate limiting - waiting', { waitTime });
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.lastRequestTime = Date.now();
    this.requestCount++;
  }

  /**
   * Execute HLTV request with retry logic
   */
  private async executeRequest<T>(
    requestFn: () => Promise<T>,
    endpoint: string,
    options: HLTVRequestOptions = {}
  ): Promise<T> {
    const { retries = config.hltv.retryAttempts, delay = config.hltv.retryDelay } = options;

    await this.rateLimit();

    const startTime = Date.now();
    Logger.apiRequest(endpoint);

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        const result = await requestFn();
        const duration = Date.now() - startTime;

        Logger.apiResponse(endpoint, duration, true);
        Logger.performance('hltv_request', duration, { endpoint, attempt });

        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        const isLastAttempt = attempt === retries;

        if (isLastAttempt) {
          Logger.apiResponse(endpoint, duration, false, (error as Error).message);
          Logger.error(`HLTV request failed after ${retries} attempts`, error as Error, {
            endpoint,
            attempts: retries,
          });
          throw error;
        }

        Logger.warn(`HLTV request attempt ${attempt} failed, retrying`, {
          endpoint,
          error: (error as Error).message,
          nextAttemptIn: delay,
        });

        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw new Error(`Failed to execute request after ${retries} attempts`);
  }

  /**
   * Get matches for a specific date
   */
  async getMatches(date?: Date): Promise<any[]> {
    const endpoint = 'getMatches';

    return this.executeRequest(
      () => HLTV.getMatches(),
      endpoint
    );
  }

  /**
   * Get detailed match information
   */
  async getMatch(matchId: number): Promise<any> {
    const endpoint = `getMatch/${matchId}`;

    return this.executeRequest(
      () => HLTV.getMatch({ id: matchId }),
      endpoint
    );
  }

  /**
   * Get team ranking
   */
  async getTeamRanking(options?: any): Promise<any[]> {
    const endpoint = 'getTeamRanking';

    return this.executeRequest(
      () => HLTV.getTeamRanking(options),
      endpoint
    );
  }

  /**
   * Get team information
   */
  async getTeam(teamId: number): Promise<any> {
    const endpoint = `getTeam/${teamId}`;

    return this.executeRequest(
      () => HLTV.getTeam({ id: teamId }),
      endpoint
    );
  }

  /**
   * Get team statistics
   */
  async getTeamStats(teamId: number, options?: {
    startDate?: string;
    endDate?: string;
    matchType?: any;
    rankingFilter?: any;
    maps?: any[];
    bestOfX?: any;
  }): Promise<any> {
    const endpoint = `getTeamStats/${teamId}`;

    return this.executeRequest(
      () => HLTV.getTeamStats({ id: teamId, ...options }),
      endpoint
    );
  }

  /**
   * Get player information
   */
  async getPlayer(playerId: number): Promise<any> {
    const endpoint = `getPlayer/${playerId}`;

    return this.executeRequest(
      () => HLTV.getPlayer({ id: playerId }),
      endpoint
    );
  }

  /**
   * Get player statistics
   */
  async getPlayerStats(playerId: number, options?: {
    startDate?: string;
    endDate?: string;
    matchType?: any;
    rankingFilter?: any;
    maps?: any[];
    bestOfX?: any;
    eventIds?: number[];
  }): Promise<any> {
    const endpoint = `getPlayerStats/${playerId}`;

    return this.executeRequest(
      () => HLTV.getPlayerStats({ id: playerId, ...options }),
      endpoint
    );
  }

  /**
   * Get player ranking
   */
  async getPlayerRanking(options?: {
    startDate?: string;
    endDate?: string;
    matchType?: any;
    rankingFilter?: any;
    maps?: any[];
    minMapCount?: number;
    countries?: string[];
    bestOfX?: any;
  }): Promise<any[]> {
    const endpoint = 'getPlayerRanking';

    return this.executeRequest(
      () => HLTV.getPlayerRanking(options),
      endpoint
    );
  }

  /**
   * Get match results
   */
  async getResults(options?: any): Promise<any[]> {
    const endpoint = 'getResults';

    return this.executeRequest(
      () => HLTV.getResults(options || {}),
      endpoint
    );
  }

  /**
   * Get events
   */
  async getEvents(options?: {
    eventType?: any;
    prizePoolMin?: number;
    prizePoolMax?: number;
    attendingTeamIds?: number[];
    attendingPlayerIds?: number[];
  }): Promise<any[]> {
    const endpoint = 'getEvents';

    return this.executeRequest(
      () => HLTV.getEvents(options),
      endpoint
    );
  }

  /**
   * Get event details
   */
  async getEvent(eventId: number): Promise<any> {
    const endpoint = `getEvent/${eventId}`;

    return this.executeRequest(
      () => HLTV.getEvent({ id: eventId }),
      endpoint
    );
  }

  /**
   * Get match statistics
   */
  async getMatchStats(matchId: number): Promise<any> {
    const endpoint = `getMatchStats/${matchId}`;

    return this.executeRequest(
      () => HLTV.getMatchStats({ id: matchId }),
      endpoint
    );
  }

  /**
   * Get request statistics
   */
  getRequestStats(): { count: number; lastRequestTime: number } {
    return {
      count: this.requestCount,
      lastRequestTime: this.lastRequestTime,
    };
  }

  /**
   * Reset request statistics
   */
  resetStats(): void {
    this.requestCount = 0;
    this.lastRequestTime = 0;
    Logger.debug('HLTV client stats reset');
  }
}
