import { Match } from '@/models/match';
import { TeamStats } from '@/models/team';
import { PlayerStats } from '@/models/player';
import { BettingOpportunity } from '@/models/betting';
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    completeness: number;
}
export declare class ValidationUtils {
    /**
     * Validate match data completeness and quality
     */
    static validateMatch(match: Match): ValidationResult;
    /**
     * Validate team statistics data
     */
    static validateTeamStats(teamStats: TeamStats): ValidationResult;
    /**
     * Validate player statistics data
     */
    static validatePlayerStats(playerStats: PlayerStats): ValidationResult;
    /**
     * Validate betting opportunity
     */
    static validateBettingOpportunity(opportunity: BettingOpportunity): ValidationResult;
    /**
     * Validate odds format and reasonableness
     */
    static validateOdds(odds: number): ValidationResult;
}
//# sourceMappingURL=validation.d.ts.map