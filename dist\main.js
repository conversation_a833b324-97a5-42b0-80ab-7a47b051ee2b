#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// Register path mapping for compiled JavaScript
require("tsconfig-paths/register");
const commander_1 = require("commander");
const chalk_1 = __importDefault(require("chalk"));
const match_analyzer_1 = require("@/analysis/match-analyzer");
const report_generator_1 = require("@/services/report-generator");
const date_utils_1 = require("@/utils/date-utils");
const logger_1 = require("@/utils/logger");
const settings_1 = require("@/config/settings");
const cache_service_1 = require("@/services/cache-service");
const program = new commander_1.Command();
// CLI Configuration
program
    .name('cs2-betting-analytics')
    .description('Ultimate CS2 betting analytics system using HLTV data')
    .version('1.0.0');
// Main analysis command
program
    .command('analyze')
    .description('Analyze CS2 matches for betting opportunities')
    .option('-d, --date <date>', 'Analysis date (today, tomorrow, or YYYY-MM-DD)', 'today')
    .option('-f, --format <format>', 'Output format (console, json, html)', 'console')
    .option('-s, --save', 'Save report to file', false)
    .option('--no-cache', 'Disable caching', false)
    .option('--force-refresh', 'Force refresh cached data', false)
    .option('--verbose', 'Enable verbose logging', false)
    .action(async (options) => {
    try {
        await runAnalysis(options);
    }
    catch (error) {
        console.error(chalk_1.default.red('❌ Analysis failed:'), error.message);
        process.exit(1);
    }
});
// Cache management commands
program
    .command('cache')
    .description('Cache management operations')
    .option('--clear', 'Clear all cache')
    .option('--stats', 'Show cache statistics')
    .option('--clear-type <type>', 'Clear specific cache type (matches, teams, players, rankings)')
    .action(async (options) => {
    try {
        await manageCacheOperations(options);
    }
    catch (error) {
        console.error(chalk_1.default.red('❌ Cache operation failed:'), error.message);
        process.exit(1);
    }
});
// Configuration command
program
    .command('config')
    .description('Show current configuration')
    .action(() => {
    showConfiguration();
});
// Test command for development
program
    .command('test')
    .description('Test HLTV API connectivity')
    .action(async () => {
    try {
        await testConnectivity();
    }
    catch (error) {
        console.error(chalk_1.default.red('❌ Connectivity test failed:'), error.message);
        process.exit(1);
    }
});
/**
 * Main analysis function
 */
async function runAnalysis(options) {
    // Setup logging
    if (options.verbose) {
        process.env.LOG_LEVEL = 'debug';
    }
    console.log(chalk_1.default.bold.cyan('🎯 CS2 Betting Analytics System'));
    console.log(chalk_1.default.gray('═'.repeat(50)));
    console.log('');
    // Parse date
    let dateMode;
    let customDate;
    if (options.date === 'today') {
        dateMode = 'today';
    }
    else if (options.date === 'tomorrow') {
        dateMode = 'tomorrow';
    }
    else {
        dateMode = 'custom';
        customDate = options.date;
    }
    const analysisDate = date_utils_1.DateUtils.getAnalysisDate(dateMode, customDate);
    // Validate date
    if (!date_utils_1.DateUtils.isReasonableAnalysisDate(analysisDate)) {
        throw new Error('Analysis date is too far in the future or invalid');
    }
    console.log(chalk_1.default.blue('📅 Analysis Configuration:'));
    console.log(`   Date: ${chalk_1.default.white(date_utils_1.DateUtils.formatForDisplay(analysisDate))}`);
    console.log(`   Format: ${chalk_1.default.white(options.format)}`);
    console.log(`   Cache: ${chalk_1.default.white(options.cache ? 'Enabled' : 'Disabled')}`);
    console.log(`   Force Refresh: ${chalk_1.default.white(options.forceRefresh ? 'Yes' : 'No')}`);
    console.log('');
    // Initialize analyzer
    console.log(chalk_1.default.yellow('🔄 Initializing analysis engine...'));
    const analyzer = new match_analyzer_1.MatchAnalyzer();
    // Show cache stats if enabled
    if (options.cache) {
        const cacheStats = cache_service_1.cacheService.getStats();
        console.log(chalk_1.default.gray(`   Cache: ${cacheStats.keys} keys, ${cacheStats.hitRate.toFixed(1)}% hit rate`));
    }
    console.log('');
    // Run analysis
    console.log(chalk_1.default.yellow('🚀 Starting analysis...'));
    const startTime = Date.now();
    const report = await analyzer.analyzeMatchesForDate(analysisDate);
    const duration = Date.now() - startTime;
    console.log(chalk_1.default.green(`✅ Analysis completed in ${duration}ms`));
    console.log('');
    // Generate and display report
    const outputFormat = options.format;
    if (outputFormat === 'console') {
        report_generator_1.ReportGenerator.displayReport(report);
    }
    else {
        const reportContent = report_generator_1.ReportGenerator.generateReport(report, outputFormat);
        console.log(reportContent);
    }
    // Save report if requested
    if (options.save) {
        console.log(chalk_1.default.blue('💾 Saving report...'));
        const filepath = await report_generator_1.ReportGenerator.saveReport(report, outputFormat);
        console.log(chalk_1.default.green(`📄 Report saved: ${filepath}`));
    }
    // Show final stats
    console.log('');
    console.log(chalk_1.default.gray('📊 Session Statistics:'));
    console.log(chalk_1.default.gray(`   Execution Time: ${duration}ms`));
    console.log(chalk_1.default.gray(`   API Requests: ${report.metadata.apiRequestsUsed}`));
    console.log(chalk_1.default.gray(`   Opportunities Found: ${report.opportunities.length}`));
    if (options.cache) {
        const finalCacheStats = cache_service_1.cacheService.getStats();
        console.log(chalk_1.default.gray(`   Cache Hit Rate: ${finalCacheStats.hitRate.toFixed(1)}%`));
    }
}
/**
 * Cache management operations
 */
async function manageCacheOperations(options) {
    console.log(chalk_1.default.bold.blue('🗄️  Cache Management'));
    console.log(chalk_1.default.gray('═'.repeat(30)));
    console.log('');
    if (options.stats) {
        const stats = cache_service_1.cacheService.getStats();
        console.log(chalk_1.default.yellow('📊 Cache Statistics:'));
        console.log(`   Total Keys: ${chalk_1.default.white(stats.keys)}`);
        console.log(`   Cache Hits: ${chalk_1.default.white(stats.hits)}`);
        console.log(`   Cache Misses: ${chalk_1.default.white(stats.misses)}`);
        console.log(`   Hit Rate: ${chalk_1.default.white(stats.hitRate.toFixed(2))}%`);
        console.log(`   Memory Size: ${chalk_1.default.white(Math.round(stats.size / 1024))} KB`);
    }
    if (options.clear) {
        cache_service_1.cacheService.clearAll();
        console.log(chalk_1.default.green('✅ All cache cleared'));
    }
    if (options.clearType) {
        const validTypes = ['matches', 'teams', 'players', 'rankings', 'team_stats', 'player_stats', 'match_details', 'events'];
        if (!validTypes.includes(options.clearType)) {
            throw new Error(`Invalid cache type. Valid types: ${validTypes.join(', ')}`);
        }
        cache_service_1.cacheService.clearType(options.clearType);
        console.log(chalk_1.default.green(`✅ Cache type '${options.clearType}' cleared`));
    }
}
/**
 * Show current configuration
 */
function showConfiguration() {
    console.log(chalk_1.default.bold.blue('⚙️  Current Configuration'));
    console.log(chalk_1.default.gray('═'.repeat(40)));
    console.log('');
    console.log(chalk_1.default.yellow('🌐 HLTV API Settings:'));
    console.log(`   Request Delay: ${chalk_1.default.white(settings_1.config.hltv.requestDelay)}ms`);
    console.log(`   Max Concurrent: ${chalk_1.default.white(settings_1.config.hltv.maxConcurrentRequests)}`);
    console.log(`   Retry Attempts: ${chalk_1.default.white(settings_1.config.hltv.retryAttempts)}`);
    console.log('');
    console.log(chalk_1.default.yellow('🗄️  Cache Settings:'));
    console.log(`   Matches TTL: ${chalk_1.default.white(settings_1.config.cache.ttl.matches / 1000)}s`);
    console.log(`   Teams TTL: ${chalk_1.default.white(settings_1.config.cache.ttl.teams / 1000)}s`);
    console.log(`   Players TTL: ${chalk_1.default.white(settings_1.config.cache.ttl.players / 1000)}s`);
    console.log(`   Rankings TTL: ${chalk_1.default.white(settings_1.config.cache.ttl.rankings / 1000)}s`);
    console.log('');
    console.log(chalk_1.default.yellow('🎯 Analysis Settings:'));
    console.log(`   Min Confidence: ${chalk_1.default.white(settings_1.config.analysis.minConfidenceThreshold)}%`);
    console.log(`   Max Confidence: ${chalk_1.default.white(settings_1.config.analysis.maxConfidenceThreshold)}%`);
    console.log(`   Min Positive EV: ${chalk_1.default.white(settings_1.config.analysis.minPositiveEvThreshold * 100)}%`);
    console.log(`   Default Stake: $${chalk_1.default.white(settings_1.config.analysis.defaultStakeAmount)}`);
    console.log('');
    console.log(chalk_1.default.yellow('⚠️  Risk Thresholds:'));
    console.log(`   Low Risk Ranking Gap: ${chalk_1.default.white(settings_1.config.risk.low.rankingGap)}`);
    console.log(`   Medium Risk Ranking Gap: ${chalk_1.default.white(settings_1.config.risk.medium.rankingGap)}`);
    console.log(`   Min Win Rate (Low): ${chalk_1.default.white(settings_1.config.risk.low.favoredTeamWinRate)}%`);
    console.log(`   Min Win Rate (Medium): ${chalk_1.default.white(settings_1.config.risk.medium.favoredTeamWinRate)}%`);
    console.log('');
    console.log(chalk_1.default.yellow('📁 Output Settings:'));
    console.log(`   Format: ${chalk_1.default.white(settings_1.config.output.format)}`);
    console.log(`   Save Reports: ${chalk_1.default.white(settings_1.config.output.saveReports ? 'Yes' : 'No')}`);
    console.log(`   Reports Directory: ${chalk_1.default.white(settings_1.config.output.reportsDir)}`);
}
/**
 * Test HLTV API connectivity
 */
async function testConnectivity() {
    console.log(chalk_1.default.bold.blue('🔌 Testing HLTV API Connectivity'));
    console.log(chalk_1.default.gray('═'.repeat(40)));
    console.log('');
    const { HLTVClient } = await Promise.resolve().then(() => __importStar(require('@/api/hltv-client')));
    const client = new HLTVClient();
    try {
        console.log(chalk_1.default.yellow('📡 Testing team rankings endpoint...'));
        const rankings = await client.getTeamRanking();
        console.log(chalk_1.default.green(`✅ Team rankings: ${rankings.length} teams fetched`));
        console.log(chalk_1.default.yellow('📡 Testing matches endpoint...'));
        const matches = await client.getMatches();
        console.log(chalk_1.default.green(`✅ Matches: ${matches.length} matches fetched`));
        console.log('');
        console.log(chalk_1.default.green('🎉 All connectivity tests passed!'));
        const stats = client.getRequestStats();
        console.log(chalk_1.default.gray(`📊 Requests made: ${stats.count}`));
    }
    catch (error) {
        console.log(chalk_1.default.red('❌ Connectivity test failed'));
        throw error;
    }
}
/**
 * Handle uncaught errors
 */
process.on('uncaughtException', (error) => {
    logger_1.Logger.error('Uncaught exception', error);
    console.error(chalk_1.default.red('💥 Fatal error:'), error.message);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    logger_1.Logger.error('Unhandled rejection', new Error(String(reason)), { promise });
    console.error(chalk_1.default.red('💥 Unhandled promise rejection:'), reason);
    process.exit(1);
});
// Parse command line arguments
program.parse();
// If no command provided, show help
if (!process.argv.slice(2).length) {
    program.outputHelp();
}
//# sourceMappingURL=main.js.map