{"version": 3, "file": "math-utils.js", "sourceRoot": "", "sources": ["../../src/utils/math-utils.ts"], "names": [], "mappings": ";;;AAAA,MAAa,SAAS;IACpB;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,KAAa,EAAE,KAAa,EAAE,WAAmB,CAAC;QAClE,IAAI,KAAK,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;IAC7F,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,IAAY,EAAE,MAAc;QACzC,MAAM,KAAK,GAAG,IAAI,GAAG,MAAM,CAAC;QAC5B,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,WAAmB;QAC3C,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,aAAa,CAClB,mBAA2B,EAC3B,WAAmB,EACnB,QAAgB,GAAG;QAEnB,MAAM,kBAAkB,GAAG,mBAAmB,GAAG,GAAG,CAAC;QACrD,MAAM,eAAe,GAAG,CAAC,GAAG,kBAAkB,CAAC;QAC/C,MAAM,SAAS,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAC5C,MAAM,UAAU,GAAG,KAAK,CAAC;QAEzB,MAAM,EAAE,GAAG,CAAC,kBAAkB,GAAG,SAAS,CAAC,GAAG,CAAC,eAAe,GAAG,UAAU,CAAC,CAAC;QAC7E,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,uBAAuB,CAC5B,mBAA2B,EAC3B,WAAmB;QAEnB,MAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,mBAAmB,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,mBAA2B,EAAE,WAAmB;QACnE,OAAO,IAAI,CAAC,aAAa,CAAC,mBAAmB,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CACnB,mBAA2B,EAC3B,WAAmB,EACnB,QAAgB;QAEhB,MAAM,CAAC,GAAG,mBAAmB,GAAG,GAAG,CAAC;QACpC,MAAM,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC;QAC1B,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAEhB,MAAM,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACtC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,aAAa,GAAG,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,MAAgB,EAAE,OAAiB;QACxD,IAAI,MAAM,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,GAAG,OAAO,CAAC,KAAK,CAAE,EAAE,CAAC,CAAC,CAAC;QAC3F,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC;QAErE,OAAO,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,WAAW,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,MAAgB;QACvC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAElC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QAC3E,MAAM,kBAAkB,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1E,MAAM,QAAQ,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QAEzF,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,MAAgB;QAC5C,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAElC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QAC3E,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAE9C,OAAO,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CACvB,MAAgB,EAChB,kBAA0B,IAAI;QAE9B,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;QACzC,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QAC3E,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAC9C,MAAM,aAAa,GAAG,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAExD,wDAAwD;QACxD,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,uCAAuC;QAC5D,MAAM,aAAa,GAAG,MAAM,GAAG,aAAa,CAAC;QAE7C,OAAO;YACL,KAAK,EAAE,IAAI,GAAG,aAAa;YAC3B,KAAK,EAAE,IAAI,GAAG,aAAa;YAC3B,IAAI,EAAE,IAAI;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,KAAa,EAAE,GAAW,EAAE,GAAW;QACtD,IAAI,GAAG,KAAK,GAAG;YAAE,OAAO,EAAE,CAAC,CAAC,kCAAkC;QAC9D,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,MAAgB,EAAE,MAAc;QACnD,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,CAAC;QAED,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChD,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YACzD,MAAM,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC;YACzE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,KAAa,EAAE,WAAmB,CAAC;QAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,KAAa,EAAE,GAAW,EAAE,GAAW;QAClD,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IAC7C,CAAC;CACF;AApLD,8BAoLC"}