export interface MatchTeam {
    id: number;
    name: string;
    logo?: string;
    ranking?: number;
    players?: MatchPlayer[];
}
export interface MatchPlayer {
    id: number;
    name: string;
    nickname: string;
    rating?: number;
    kpr?: number;
    adr?: number;
    kast?: number;
}
export interface MatchFormat {
    type: 'bo1' | 'bo3' | 'bo5';
    maps: string[];
}
export interface MatchEvent {
    id: number;
    name: string;
    stage?: string;
    prizePool?: number;
    tier?: 'S' | 'A' | 'B' | 'C' | 'D';
}
export interface MatchOdds {
    team1: number;
    team2: number;
    source: string;
    timestamp: Date;
    markets?: {
        mapHandicap?: {
            team1: number;
            team2: number;
            line: number;
        };
        totalMaps?: {
            over: number;
            under: number;
            line: number;
        };
        correctScore?: {
            [score: string]: number;
        };
    };
}
export interface Match {
    id: number;
    date: Date;
    team1: MatchTeam;
    team2: MatchTeam;
    format: MatchFormat;
    event: MatchEvent;
    odds?: MatchOdds;
    status: 'upcoming' | 'live' | 'finished';
    significance?: number;
    hasLineup?: boolean;
    streams?: Array<{
        name: string;
        link: string;
        viewers?: number;
    }>;
}
export interface MatchResult {
    matchId: number;
    winner: 'team1' | 'team2';
    score: {
        team1: number;
        team2: number;
    };
    maps: Array<{
        name: string;
        team1Score: number;
        team2Score: number;
        winner: 'team1' | 'team2';
    }>;
    duration?: number;
    date: Date;
}
//# sourceMappingURL=match.d.ts.map