import { MathUtils } from '@/utils/math-utils';

describe('MathUtils', () => {
  describe('percentage', () => {
    it('should calculate percentage correctly', () => {
      expect(MathUtils.percentage(25, 100)).toBe(25.0);
      expect(MathUtils.percentage(1, 3, 2)).toBe(33.33);
      expect(MathUtils.percentage(0, 100)).toBe(0);
    });

    it('should handle division by zero', () => {
      expect(MathUtils.percentage(10, 0)).toBe(0);
    });
  });

  describe('winRate', () => {
    it('should calculate win rate correctly', () => {
      expect(MathUtils.winRate(7, 3)).toBe(70.0);
      expect(MathUtils.winRate(0, 5)).toBe(0);
      expect(MathUtils.winRate(5, 0)).toBe(100.0);
    });

    it('should handle no games played', () => {
      expect(MathUtils.winRate(0, 0)).toBe(0);
    });
  });

  describe('impliedProbability', () => {
    it('should calculate implied probability from decimal odds', () => {
      expect(MathUtils.impliedProbability(2.0)).toBe(50.0);
      expect(MathUtils.impliedProbability(1.5)).toBe(66.67);
      expect(MathUtils.impliedProbability(4.0)).toBe(25.0);
    });
  });

  describe('expectedValue', () => {
    it('should calculate expected value correctly', () => {
      // 60% chance to win, 2.0 odds, $100 stake
      // EV = (0.6 * $100) - (0.4 * $100) = $20
      expect(MathUtils.expectedValue(60, 2.0, 100)).toBe(20);

      // 40% chance to win, 2.0 odds, $100 stake
      // EV = (0.4 * $100) - (0.6 * $100) = -$20
      expect(MathUtils.expectedValue(40, 2.0, 100)).toBe(-20);
    });

    it('should handle edge cases', () => {
      expect(MathUtils.expectedValue(0, 2.0, 100)).toBe(-100);
      expect(MathUtils.expectedValue(100, 2.0, 100)).toBe(100);
    });
  });

  describe('hasPositiveEV', () => {
    it('should identify positive EV correctly', () => {
      expect(MathUtils.hasPositiveEV(60, 2.0)).toBe(true);
      expect(MathUtils.hasPositiveEV(40, 2.0)).toBe(false);
      expect(MathUtils.hasPositiveEV(50, 2.0)).toBe(false);
    });
  });

  describe('kellyCriterion', () => {
    it('should calculate Kelly fraction correctly', () => {
      // 60% probability, 2.0 odds (1.0 profit), $1000 bankroll
      // Kelly = ((1.0 * 0.6) - 0.4) / 1.0 = 0.2
      // Recommended bet = 0.2 * $1000 = $200
      expect(MathUtils.kellyCriterion(60, 2.0, 1000)).toBeCloseTo(200, 0);
    });

    it('should return 0 for negative Kelly', () => {
      expect(MathUtils.kellyCriterion(40, 2.0, 1000)).toBe(0);
    });
  });

  describe('weightedAverage', () => {
    it('should calculate weighted average correctly', () => {
      const values = [10, 20, 30];
      const weights = [1, 2, 3];
      // (10*1 + 20*2 + 30*3) / (1+2+3) = 140/6 = 23.33
      expect(MathUtils.weightedAverage(values, weights)).toBeCloseTo(23.33, 2);
    });

    it('should handle equal weights', () => {
      const values = [10, 20, 30];
      const weights = [1, 1, 1];
      expect(MathUtils.weightedAverage(values, weights)).toBe(20);
    });

    it('should throw error for mismatched arrays', () => {
      expect(() => {
        MathUtils.weightedAverage([1, 2], [1, 2, 3]);
      }).toThrow();
    });
  });

  describe('standardDeviation', () => {
    it('should calculate standard deviation correctly', () => {
      const values = [2, 4, 4, 4, 5, 5, 7, 9];
      const result = MathUtils.standardDeviation(values);
      expect(result).toBeCloseTo(2, 0);
    });

    it('should handle empty array', () => {
      expect(MathUtils.standardDeviation([])).toBe(0);
    });

    it('should handle single value', () => {
      expect(MathUtils.standardDeviation([5])).toBe(0);
    });
  });

  describe('clamp', () => {
    it('should clamp values correctly', () => {
      expect(MathUtils.clamp(5, 0, 10)).toBe(5);
      expect(MathUtils.clamp(-5, 0, 10)).toBe(0);
      expect(MathUtils.clamp(15, 0, 10)).toBe(10);
    });
  });

  describe('round', () => {
    it('should round to specified decimal places', () => {
      expect(MathUtils.round(3.14159, 2)).toBe(3.14);
      expect(MathUtils.round(3.14159, 0)).toBe(3);
      expect(MathUtils.round(3.14159, 4)).toBe(3.1416);
    });
  });

  describe('normalize', () => {
    it('should normalize values to 0-100 scale', () => {
      expect(MathUtils.normalize(5, 0, 10)).toBe(50);
      expect(MathUtils.normalize(0, 0, 10)).toBe(0);
      expect(MathUtils.normalize(10, 0, 10)).toBe(100);
    });

    it('should handle equal min and max', () => {
      expect(MathUtils.normalize(5, 5, 5)).toBe(50);
    });
  });
});
