export interface TeamAnalysisResult {
    teamId: number;
    name: string;
    ranking: number;
    overallRating: number;
    form: {
        recent: {
            winRate: number;
            matches: number;
            trend: 'improving' | 'declining' | 'stable';
            consistency: number;
        };
        overall: {
            winRate: number;
            totalMatches: number;
        };
    };
    performance: {
        openingKills: number;
        tradeKillEfficiency: number;
        economyManagement: number;
        clutchPerformance: number;
        pistolRounds: number;
        mapControl: number;
    };
    mapPool: {
        strongMaps: Array<{
            map: string;
            winRate: number;
            confidence: number;
        }>;
        weakMaps: Array<{
            map: string;
            winRate: number;
            confidence: number;
        }>;
        overallAdvantage: number;
    };
    roster: {
        stability: number;
        experience: number;
        averageRating: number;
        keyPlayers: Array<{
            name: string;
            role: string;
            rating: number;
            impact: number;
        }>;
    };
    contextualFactors: {
        patchAdaptation: number;
        motivationLevel: number;
        travelImpact: number;
        pressureHandling: number;
    };
    strengths: string[];
    weaknesses: string[];
    dataQuality: {
        completeness: number;
        reliability: 'high' | 'medium' | 'low';
        sampleSize: number;
        lastUpdated: Date;
    };
}
export declare class TeamAnalyzer {
    /**
     * Analyze team performance and characteristics
     */
    static analyzeTeam(teamData: any, teamStats: any, rankings: any[]): TeamAnalysisResult;
    /**
     * Extract team ranking from rankings data
     */
    private static extractRanking;
    /**
     * Calculate overall team rating
     */
    private static calculateOverallRating;
    /**
     * Analyze team form and recent performance
     */
    private static analyzeForm;
    /**
     * Analyze performance trends
     */
    private static analyzeTrend;
    /**
     * Analyze team performance metrics
     */
    private static analyzePerformance;
    /**
     * Analyze team map pool
     */
    private static analyzeMapPool;
    /**
     * Calculate map confidence based on sample size and win rate
     */
    private static calculateMapConfidence;
    /**
     * Calculate overall map pool advantage
     */
    private static calculateMapPoolAdvantage;
    /**
     * Analyze roster characteristics
     */
    private static analyzeRoster;
    /**
     * Calculate roster stability
     */
    private static calculateRosterStability;
    /**
     * Calculate average team experience
     */
    private static calculateAverageExperience;
    /**
     * Calculate average team rating
     */
    private static calculateAverageRating;
    /**
     * Identify key players
     */
    private static identifyKeyPlayers;
    /**
     * Calculate player impact score
     */
    private static calculatePlayerImpact;
    /**
     * Analyze contextual factors
     */
    private static analyzeContextualFactors;
    /**
     * Assess data quality
     */
    private static assessDataQuality;
    /**
     * Calculate data completeness percentage
     */
    private static calculateDataCompleteness;
    /**
     * Identify team strengths
     */
    private static identifyStrengths;
    /**
     * Identify team weaknesses
     */
    private static identifyWeaknesses;
}
//# sourceMappingURL=team-analyzer.d.ts.map