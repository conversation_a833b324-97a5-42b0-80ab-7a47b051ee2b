{"version": 3, "file": "settings.js", "sourceRoot": "", "sources": ["../../src/config/settings.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAG5B,gBAAM,CAAC,MAAM,EAAE,CAAC;AAgDhB,MAAM,YAAY,GAAG,CAAC,GAAW,EAAE,YAAoB,EAAU,EAAE;IACjE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/B,OAAO,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;AACpD,CAAC,CAAC;AAEF,MAAM,YAAY,GAAG,CAAC,GAAW,EAAE,YAAoB,EAAU,EAAE;IACjE,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC;AAC1C,CAAC,CAAC;AAEF,MAAM,aAAa,GAAG,CAAC,GAAW,EAAE,YAAqB,EAAW,EAAE;IACpE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/B,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC;AAC/D,CAAC,CAAC;AAEW,QAAA,MAAM,GAAc;IAC/B,IAAI,EAAE;QACJ,YAAY,EAAE,YAAY,CAAC,oBAAoB,EAAE,IAAI,CAAC;QACtD,qBAAqB,EAAE,YAAY,CAAC,8BAA8B,EAAE,CAAC,CAAC;QACtE,aAAa,EAAE,YAAY,CAAC,qBAAqB,EAAE,CAAC,CAAC;QACrD,UAAU,EAAE,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC;KACnD;IAED,KAAK,EAAE;QACL,GAAG,EAAE;YACH,OAAO,EAAE,YAAY,CAAC,mBAAmB,EAAE,MAAM,CAAC,EAAE,YAAY;YAChE,KAAK,EAAE,YAAY,CAAC,iBAAiB,EAAE,OAAO,CAAC,EAAE,aAAa;YAC9D,OAAO,EAAE,YAAY,CAAC,mBAAmB,EAAE,OAAO,CAAC,EAAE,SAAS;YAC9D,QAAQ,EAAE,YAAY,CAAC,oBAAoB,EAAE,OAAO,CAAC,EAAE,UAAU;SAClE;KACF;IAED,QAAQ,EAAE;QACR,sBAAsB,EAAE,YAAY,CAAC,0BAA0B,EAAE,EAAE,CAAC;QACpE,sBAAsB,EAAE,YAAY,CAAC,0BAA0B,EAAE,EAAE,CAAC;QACpE,kBAAkB,EAAE,YAAY,CAAC,sBAAsB,EAAE,GAAG,CAAC;QAC7D,sBAAsB,EAAE,YAAY,CAAC,2BAA2B,EAAE,CAAC,CAAC,GAAG,GAAG;KAC3E;IAED,OAAO,EAAE;QACP,KAAK,EAAE,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC;QACxC,QAAQ,EAAE,YAAY,CAAC,eAAe,EAAE,wBAAwB,CAAC;KAClE;IAED,MAAM,EAAE;QACN,MAAM,EAAE,YAAY,CAAC,eAAe,EAAE,SAAS,CAAgC;QAC/E,WAAW,EAAE,aAAa,CAAC,cAAc,EAAE,IAAI,CAAC;QAChD,UAAU,EAAE,YAAY,CAAC,aAAa,EAAE,WAAW,CAAC;KACrD;IAED,IAAI,EAAE;QACJ,GAAG,EAAE;YACH,UAAU,EAAE,YAAY,CAAC,sBAAsB,EAAE,EAAE,CAAC;YACpD,kBAAkB,EAAE,YAAY,CAAC,uBAAuB,EAAE,EAAE,CAAC;YAC7D,eAAe,EAAE,CAAC,EAAE,WAAW;YAC/B,iBAAiB,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;YACjC,gBAAgB,EAAE,EAAE;YACpB,UAAU,EAAE,YAAY,CAAC,0BAA0B,EAAE,GAAG,CAAC,GAAG,GAAG;YAC/D,aAAa,EAAE,EAAE;YACjB,sBAAsB,EAAE;gBACtB,yBAAyB;gBACzB,6BAA6B;gBAC7B,0BAA0B;aAC3B;SACF;QAED,MAAM,EAAE;YACN,UAAU,EAAE,YAAY,CAAC,yBAAyB,EAAE,CAAC,CAAC;YACtD,kBAAkB,EAAE,YAAY,CAAC,0BAA0B,EAAE,EAAE,CAAC;YAChE,eAAe,EAAE,CAAC,EAAE,UAAU;YAC9B,gBAAgB,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;YAChC,gBAAgB,EAAE,EAAE;YACpB,UAAU,EAAE,YAAY,CAAC,6BAA6B,EAAE,GAAG,CAAC,GAAG,GAAG;YAClE,aAAa,EAAE,EAAE;SAClB;QAED,SAAS,EAAE;YACT,UAAU,EAAE,EAAE;YACd,qBAAqB,EAAE,EAAE,EAAE,OAAO;YAClC,eAAe,EAAE,IAAI;YACrB,kBAAkB,EAAE,CAAC,KAAK,CAAC;SAC5B;KACF;IAED,OAAO,EAAE;QACP,kBAAkB,EAAE,YAAY,CAAC,uBAAuB,EAAE,EAAE,CAAC;QAC7D,sBAAsB,EAAE,YAAY,CAAC,2BAA2B,EAAE,EAAE,CAAC;QACrE,iBAAiB,EAAE,YAAY,CAAC,sBAAsB,EAAE,EAAE,CAAC;QAC3D,oBAAoB,EAAE,YAAY,CAAC,yBAAyB,EAAE,EAAE,CAAC;QACjE,gBAAgB,EAAE,YAAY,CAAC,qBAAqB,EAAE,EAAE,CAAC;KAC1D;CACF,CAAC;AAEF,kBAAe,cAAM,CAAC"}