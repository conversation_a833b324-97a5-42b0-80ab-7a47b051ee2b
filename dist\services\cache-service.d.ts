export type CacheKey = 'matches' | 'teams' | 'players' | 'rankings' | 'team_stats' | 'player_stats' | 'match_details' | 'events';
export interface CacheOptions {
    ttl?: number;
    checkperiod?: number;
}
export declare class CacheService {
    private cache;
    private hitCount;
    private missCount;
    constructor(options?: CacheOptions);
    /**
     * Get TTL for specific cache type
     */
    private getTTL;
    /**
     * Generate cache key
     */
    private generateKey;
    /**
     * Get value from cache
     */
    get<T>(cacheType: CacheKey, identifier: string | number, params?: Record<string, any>): T | undefined;
    /**
     * Set value in cache
     */
    set<T>(cacheType: CacheKey, identifier: string | number, value: T, params?: Record<string, any>, customTTL?: number): boolean;
    /**
     * Delete value from cache
     */
    delete(cacheType: CacheKey, identifier: string | number, params?: Record<string, any>): number;
    /**
     * Check if key exists in cache
     */
    has(cacheType: Cache<PERSON><PERSON>, identifier: string | number, params?: Record<string, any>): boolean;
    /**
     * Get or set pattern - fetch from cache or execute function and cache result
     */
    getOrSet<T>(cacheType: Cache<PERSON>ey, identifier: string | number, fetchFunction: () => Promise<T>, params?: Record<string, any>, customTTL?: number): Promise<T>;
    /**
     * Clear all cache entries of a specific type
     */
    clearType(cacheType: CacheKey): void;
    /**
     * Clear all cache entries
     */
    clearAll(): void;
    /**
     * Get cache statistics
     */
    getStats(): {
        keys: number;
        hits: number;
        misses: number;
        hitRate: number;
        size: number;
    };
    /**
     * Get cache keys by pattern
     */
    getKeysByPattern(pattern: string): string[];
    /**
     * Get TTL for a specific key
     */
    getTTLForKey(cacheType: CacheKey, identifier: string | number, params?: Record<string, any>): number;
    /**
     * Extend TTL for a specific key
     */
    extendTTL(cacheType: CacheKey, identifier: string | number, additionalSeconds: number, params?: Record<string, any>): boolean;
    /**
     * Warm up cache with commonly accessed data
     */
    warmUp(warmUpFunctions: Array<() => Promise<void>>): Promise<void>;
}
export declare const cacheService: CacheService;
//# sourceMappingURL=cache-service.d.ts.map