export declare class MathUtils {
    /**
     * Calculate percentage with specified decimal places
     */
    static percentage(value: number, total: number, decimals?: number): number;
    /**
     * Calculate win rate from wins and losses
     */
    static winRate(wins: number, losses: number): number;
    /**
     * Calculate implied probability from decimal odds
     */
    static impliedProbability(decimalOdds: number): number;
    /**
     * Calculate expected value
     * EV = (Probability of Win × Amount Won per Bet) - (Probability of Loss × Amount Lost per Bet)
     */
    static expectedValue(assessedProbability: number, decimalOdds: number, stake?: number): number;
    /**
     * Calculate expected value as percentage
     */
    static expectedValuePercentage(assessedProbability: number, decimalOdds: number): number;
    /**
     * Check if bet has positive expected value
     */
    static hasPositiveEV(assessedProbability: number, decimalOdds: number): boolean;
    /**
     * Calculate Kelly Criterion optimal bet size
     */
    static kellyCriterion(assessedProbability: number, decimalOdds: number, bankroll: number): number;
    /**
     * Calculate average with weights
     */
    static weightedAverage(values: number[], weights: number[]): number;
    /**
     * Calculate standard deviation
     */
    static standardDeviation(values: number[]): number;
    /**
     * Calculate coefficient of variation (relative standard deviation)
     */
    static coefficientOfVariation(values: number[]): number;
    /**
     * Calculate confidence interval
     */
    static confidenceInterval(values: number[], confidenceLevel?: number): {
        lower: number;
        upper: number;
        mean: number;
    };
    /**
     * Normalize value to 0-100 scale
     */
    static normalize(value: number, min: number, max: number): number;
    /**
     * Calculate moving average
     */
    static movingAverage(values: number[], window: number): number[];
    /**
     * Round to specified decimal places
     */
    static round(value: number, decimals?: number): number;
    /**
     * Clamp value between min and max
     */
    static clamp(value: number, min: number, max: number): number;
}
//# sourceMappingURL=math-utils.d.ts.map