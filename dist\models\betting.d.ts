export type RiskLevel = 'low' | 'medium' | 'high';
export type BetType = 'match_winner' | 'map_handicap' | 'total_maps' | 'correct_score' | 'map_winner' | 'player_performance' | 'first_half_winner' | 'pistol_rounds' | 'overtime';
export interface BettingOpportunity {
    rank: number;
    match: {
        id: number;
        team1: string;
        team2: string;
        date: Date;
        tournament: string;
        stage: string;
    };
    bet: {
        type: BetType;
        selection: string;
        odds: number;
        oddsSource: string;
        stake?: number;
    };
    risk: {
        level: RiskLevel;
        factors: string[];
        mitigatingFactors: string[];
    };
    analysis: {
        confidence: number;
        expectedValue: number;
        impliedProbability: number;
        assessedProbability: number;
        summary: {
            teamRankings: string;
            recentForm: string;
            mapPoolAdvantage: string[];
            keyPlayerMatchups: string[];
            advancedMetrics: string[];
            headToHead?: string;
            tournamentContext: string[];
        };
        justification: string;
        redFlags: string[];
        oddsMovement?: {
            opening: number;
            current: number;
            direction: 'up' | 'down' | 'stable';
            significance: 'minor' | 'moderate' | 'major';
        };
    };
    dataQuality: {
        completeness: number;
        reliability: 'high' | 'medium' | 'low';
        lastUpdated: Date;
        sources: string[];
    };
}
export interface RiskCriteria {
    low: {
        rankingGap: number;
        favoredTeamWinRate: number;
        rosterStability: number;
        formatRequirement: string[];
        mapPoolAdvantage: number;
        teamRating: number;
        pistolWinRate: number;
        additionalRequirements: string[];
    };
    medium: {
        rankingGap: number;
        favoredTeamWinRate: number;
        rosterStability: number;
        formatPreference: string[];
        mapPoolAdvantage: number;
        teamRating: number;
        pistolWinRate: number;
    };
    exclusion: {
        minWinRate: number;
        maxRosterChangeWindow: number;
        minPlayerRating: number;
        problematicFormats: string[];
    };
}
export interface AnalysisReport {
    metadata: {
        analysisDate: Date;
        targetDate: string;
        totalMatches: number;
        qualifyingMatches: number;
        executionTime: number;
        apiRequestsUsed: number;
    };
    opportunities: BettingOpportunity[];
    summary: {
        topRecommendations: BettingOpportunity[];
        riskDistribution: {
            low: number;
            medium: number;
            high: number;
        };
        alternativeOptions: BettingOpportunity[];
        marketInefficiencies: string[];
    };
    dataQuality: {
        overallCompleteness: number;
        reliabilityScore: number;
        missingDataPoints: string[];
        recommendations: string[];
    };
    warnings: string[];
    disclaimers: string[];
}
//# sourceMappingURL=betting.d.ts.map