# 📖 CS2 Betting Analytics - Complete Usage Guide

This comprehensive guide will walk you through using the CS2 Betting Analytics System to identify high-value betting opportunities.

## 🚀 Quick Start

### 1. Basic Analysis
```bash
# Analyze today's matches
npx ts-node -r tsconfig-paths/register src/main.ts analyze

# Analyze tomorrow's matches
npx ts-node -r tsconfig-paths/register src/main.ts analyze --date tomorrow

# Analyze a specific date
npx ts-node -r tsconfig-paths/register src/main.ts analyze --date 2024-01-15
```

### 2. Output Formats
```bash
# Console output (default)
npx ts-node -r tsconfig-paths/register src/main.ts analyze --format console

# JSON output for programmatic use
npx ts-node -r tsconfig-paths/register src/main.ts analyze --format json

# HTML report for sharing
npx ts-node -r tsconfig-paths/register src/main.ts analyze --format html
```

### 3. Save Reports
```bash
# Save report to file
npx ts-node -r tsconfig-paths/register src/main.ts analyze --save

# Combine options
npx ts-node -r tsconfig-paths/register src/main.ts analyze --date tomorrow --format html --save
```

## 🎯 Understanding the Output

### Console Report Structure

#### 1. Analysis Metadata
- **Target Date**: The date being analyzed
- **Analysis Time**: When the analysis was performed
- **Execution Time**: How long the analysis took
- **Total Matches**: Number of matches found
- **Qualifying Matches**: Matches that met minimum criteria
- **API Requests**: Number of HLTV API calls made

#### 2. Top 5 Betting Opportunities
Each opportunity includes:
- **Rank**: Priority ranking (1-5)
- **Match**: Teams and tournament information
- **Bet Type**: Type of bet (match winner, map handicap, etc.)
- **Odds**: Current betting odds and source
- **Risk Level**: 🛡️ Low, ⚖️ Medium, or 🚫 High
- **Confidence**: System confidence (60-90%)
- **Expected Value**: Percentage advantage over bookmaker

#### 3. Analysis Summary
- **Team Rankings**: HLTV world rankings comparison
- **Recent Form**: Win rates over last 10 matches
- **Map Pool Advantage**: Strong/weak maps for each team
- **Key Player Matchups**: Star player comparisons
- **Advanced Metrics**: Opening kills, clutch rates, etc.
- **Tournament Context**: Event importance and format

#### 4. Justification
Detailed explanation of why the bet offers value, including:
- Statistical advantages
- Form analysis
- Historical performance
- Contextual factors

#### 5. Red Flags
Any concerning factors that could impact the bet:
- Recent roster changes
- Poor recent form
- Data quality issues
- Format disadvantages

## ⚠️ Risk Assessment System

### 🛡️ Low Risk Bets
**All criteria must be met:**
- HLTV ranking gap ≥ 10 places
- Favored team win rate ≥ 70% (last 10 matches)
- Stable roster (3+ months)
- BO3/BO5 format only
- Clear map pool advantage (≥65% win rate on ≥2 maps)
- Team average rating ≥ 1.10
- Pistol round win rate ≥ 55%
- No reported team issues

### ⚖️ Medium Risk Bets
**All criteria must be met:**
- HLTV ranking gap ≥ 5 places
- Favored team win rate ≥ 60% (last 10 matches)
- Stable roster (1+ month)
- BO3/BO5 preferred
- Map pool advantage (≥60% win rate on ≥1 map)
- Team average rating ≥ 1.05
- Pistol round win rate ≥ 50%

### 🚫 High Risk (Excluded)
- Teams with <55% recent win rate
- Major roster changes in past 30 days
- Insufficient verifiable data
- Key players with <0.95 rating in last 5 matches
- BO1 matches without exceptional value

## 📊 Advanced Features

### Cache Management
```bash
# View cache statistics
npx ts-node -r tsconfig-paths/register src/main.ts cache --stats

# Clear all cache
npx ts-node -r tsconfig-paths/register src/main.ts cache --clear

# Clear specific cache type
npx ts-node -r tsconfig-paths/register src/main.ts cache --clear-type teams
```

### Configuration
```bash
# View current configuration
npx ts-node -r tsconfig-paths/register src/main.ts config
```

### API Testing
```bash
# Test HLTV API connectivity
npx ts-node -r tsconfig-paths/register src/main.ts test
```

### Verbose Logging
```bash
# Enable detailed logging
npx ts-node -r tsconfig-paths/register src/main.ts analyze --verbose
```

## 🔧 Configuration Options

### Environment Variables (.env)
```env
# Rate Limiting
HLTV_REQUEST_DELAY=1000          # Delay between API requests (ms)
HLTV_MAX_CONCURRENT_REQUESTS=3   # Max concurrent requests
HLTV_RETRY_ATTEMPTS=3            # Retry attempts for failed requests

# Cache Settings
CACHE_TTL_MATCHES=300000         # Match data cache TTL (ms)
CACHE_TTL_TEAMS=1800000          # Team data cache TTL (ms)
CACHE_TTL_PLAYERS=3600000        # Player data cache TTL (ms)
CACHE_TTL_RANKINGS=7200000       # Rankings cache TTL (ms)

# Analysis Thresholds
MIN_CONFIDENCE_THRESHOLD=60      # Minimum confidence for recommendations
MAX_CONFIDENCE_THRESHOLD=90      # Maximum confidence cap
MIN_POSITIVE_EV_THRESHOLD=0.05   # Minimum positive EV (5%)
DEFAULT_STAKE_AMOUNT=100         # Default stake for calculations

# Risk Assessment
LOW_RISK_RANKING_GAP=10          # Minimum ranking gap for low risk
MEDIUM_RISK_RANKING_GAP=5        # Minimum ranking gap for medium risk
MIN_WIN_RATE_LOW_RISK=70         # Minimum win rate for low risk
MIN_WIN_RATE_MEDIUM_RISK=60      # Minimum win rate for medium risk

# Output Settings
OUTPUT_FORMAT=console            # Default output format
SAVE_REPORTS=true               # Auto-save reports
REPORTS_DIR=./reports           # Reports directory
```

## 📈 Interpreting Results

### Expected Value (EV)
- **Positive EV**: Bet offers mathematical advantage
- **Negative EV**: Bet favors the bookmaker
- **Higher EV**: Greater potential profit over time

### Confidence Levels
- **60-70%**: Moderate confidence, proceed with caution
- **70-80%**: Good confidence, solid opportunity
- **80-90%**: High confidence, strong recommendation

### Kelly Criterion
The system calculates optimal bet sizing using the Kelly Criterion:
- **Recommended Stake**: Conservative 25% of Kelly
- **Max Stake**: Full Kelly recommendation
- **Never bet more than you can afford to lose**

## 🎮 Best Practices

### 1. Data Quality
- Always check data completeness percentage
- Higher sample sizes provide more reliable analysis
- Recent roster changes reduce reliability

### 2. Risk Management
- Focus on low and medium risk opportunities
- Diversify across multiple bets
- Never chase losses

### 3. Market Timing
- Odds can change rapidly
- Early analysis may capture better value
- Monitor line movements

### 4. Contextual Factors
- Consider tournament importance
- Account for travel and fatigue
- Evaluate motivation levels

## 🚨 Important Warnings

### Data Limitations
- Analysis based on available HLTV data
- Some metrics may be incomplete
- Recent matches weighted more heavily

### Market Efficiency
- Professional betting markets are highly efficient
- Positive EV opportunities are rare
- Past performance doesn't guarantee future results

### Responsible Gambling
- This tool is for educational purposes
- Always gamble responsibly
- Set strict bankroll limits
- Seek help if gambling becomes problematic

## 🔍 Troubleshooting

### Common Issues

#### No Matches Found
- Check if matches are scheduled for the date
- Try different dates
- Verify HLTV API connectivity

#### API Errors
- Check internet connection
- Verify HLTV API is accessible
- Rate limiting may cause temporary delays

#### Cache Issues
- Clear cache if data seems stale
- Check cache statistics
- Restart application if needed

#### Performance Issues
- Reduce concurrent requests
- Increase request delays
- Clear old cache data

### Getting Help
1. Check the logs in `./logs/` directory
2. Run with `--verbose` flag for detailed output
3. Test API connectivity with `test` command
4. Review configuration with `config` command

## 📚 Additional Resources

- **HLTV.org**: Primary data source
- **Kelly Criterion**: Optimal bet sizing theory
- **Expected Value**: Mathematical betting advantage
- **Bankroll Management**: Responsible gambling practices

Remember: This system identifies opportunities based on statistical analysis. Always conduct your own research and never bet more than you can afford to lose.
