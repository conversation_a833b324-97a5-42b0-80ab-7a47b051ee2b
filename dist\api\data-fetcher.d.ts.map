{"version": 3, "file": "data-fetcher.d.ts", "sourceRoot": "", "sources": ["../../src/api/data-fetcher.ts"], "names": [], "mappings": "AAQA,MAAM,WAAW,kBAAkB;IACjC,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB,qBAAqB,CAAC,EAAE,MAAM,CAAC;CAChC;AAED,qBAAa,WAAW;IACtB,OAAO,CAAC,UAAU,CAAa;;IAM/B;;OAEG;IACG,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,GAAE,kBAAuB,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;IA4BhF;;OAEG;IACG,iBAAiB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,GAAE,kBAAuB,GAAG,OAAO,CAAC,GAAG,CAAC;IAsBxF;;OAEG;IACG,iBAAiB,CAAC,OAAO,GAAE,kBAAuB,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;IAwBzE;;OAEG;IACG,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,GAAE,kBAAuB,GAAG,OAAO,CAAC;QAC7E,IAAI,EAAE,GAAG,CAAC;QACV,KAAK,EAAE,GAAG,CAAC;KACZ,CAAC;IAwBF;;OAEG;IACG,eAAe,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,GAAE,kBAAuB,GAAG,OAAO,CAAC;QACjF,IAAI,EAAE,GAAG,CAAC;QACV,KAAK,EAAE,GAAG,CAAC;KACZ,CAAC;IAwBF;;OAEG;IACG,kBAAkB,CAAC,OAAO,GAAE;QAChC,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;QACnB,SAAS,CAAC,EAAE,IAAI,CAAC;QACjB,OAAO,CAAC,EAAE,IAAI,CAAC;QACf,KAAK,CAAC,EAAE,MAAM,CAAC;KAChB,GAAG,kBAAuB,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;IA+B5C;;OAEG;IACG,sBAAsB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,GAAE,kBAAuB,GAAG,OAAO,CAAC;QACvF,KAAK,EAAE,GAAG,CAAC;QACX,SAAS,EAAE;YAAE,IAAI,EAAE,GAAG,CAAC;YAAC,KAAK,EAAE,GAAG,CAAA;SAAE,CAAC;QACrC,SAAS,EAAE;YAAE,IAAI,EAAE,GAAG,CAAC;YAAC,KAAK,EAAE,GAAG,CAAA;SAAE,CAAC;QACrC,QAAQ,EAAE,GAAG,EAAE,CAAC;QAChB,aAAa,EAAE,GAAG,EAAE,CAAC;KACtB,CAAC;IA2CF;;OAEG;IACG,mBAAmB,CAAC,OAAO,GAAE,kBAAuB,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;IAwB3E;;OAEG;IACG,sBAAsB,CAC1B,OAAO,EAAE,MAAM,EAAE,EACjB,OAAO,GAAE,kBAAuB,GAC/B,OAAO,CAAC,KAAK,CAAC;QAAE,MAAM,EAAE,MAAM,CAAC;QAAC,IAAI,EAAE,GAAG,CAAC;QAAC,KAAK,EAAE,GAAG,CAAA;KAAE,CAAC,CAAC;IA2C5D;;OAEG;IACH,cAAc,IAAI;QAAE,QAAQ,EAAE,MAAM,CAAC;QAAC,eAAe,EAAE,MAAM,CAAC;QAAC,UAAU,EAAE,GAAG,CAAA;KAAE;CAQjF"}