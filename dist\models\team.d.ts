export interface TeamRanking {
    position: number;
    points: number;
    change: number;
    team: {
        id: number;
        name: string;
        logo?: string;
        country: string;
    };
}
export interface TeamStats {
    teamId: number;
    name: string;
    ranking: number;
    rating: number;
    wins: number;
    losses: number;
    winRate: number;
    recentForm: {
        wins: number;
        losses: number;
        winRate: number;
        matches: Array<{
            date: Date;
            opponent: string;
            result: 'win' | 'loss';
            score: string;
            maps: string[];
        }>;
    };
    mapStats: Array<{
        map: string;
        played: number;
        wins: number;
        losses: number;
        winRate: number;
        avgRounds: number;
    }>;
    performance: {
        openingKillRate: number;
        tradeKillEfficiency: number;
        economyManagement: {
            buyRoundWinRate: number;
            ecoRoundWinRate: number;
            forceRoundWinRate: number;
        };
        clutchPerformance: {
            attempts: number;
            successes: number;
            successRate: number;
        };
        pistolRounds: {
            played: number;
            won: number;
            winRate: number;
        };
        postPlantSituations: {
            attempts: number;
            successes: number;
            successRate: number;
        };
        retakeSituations: {
            attempts: number;
            successes: number;
            successRate: number;
        };
    };
    roster: {
        stable: boolean;
        lastChange?: Date;
        players: Array<{
            id: number;
            name: string;
            nickname: string;
            role: 'entry' | 'support' | 'awp' | 'igl' | 'lurker';
            rating: number;
            joinDate?: Date;
        }>;
    };
    context: {
        recentPatchAdaptation: number;
        travelImpact?: {
            timezoneDifference: number;
            arrivalDate?: Date;
        };
        motivationFactors: {
            tournamentImportance: number;
            qualificationStakes: boolean;
            prizePoolSignificance: number;
        };
    };
    dataQuality: {
        completeness: number;
        lastUpdated: Date;
        sampleSize: number;
        reliability: 'high' | 'medium' | 'low';
    };
}
//# sourceMappingURL=team.d.ts.map