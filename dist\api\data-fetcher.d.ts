export interface DataFetcherOptions {
    useCache?: boolean;
    forceRefresh?: boolean;
    maxConcurrentRequests?: number;
}
export declare class DataFetcher {
    private hltvClient;
    constructor();
    /**
     * Fetch all matches for a specific date
     */
    fetchMatches(date: Date, options?: DataFetcherOptions): Promise<any[]>;
    /**
     * Fetch detailed match information
     */
    fetchMatchDetails(matchId: number, options?: DataFetcherOptions): Promise<any>;
    /**
     * Fetch team rankings
     */
    fetchTeamRankings(options?: DataFetcherOptions): Promise<any[]>;
    /**
     * Fetch team information and statistics
     */
    fetchTeamData(teamId: number, options?: DataFetcherOptions): Promise<{
        info: any;
        stats: any;
    }>;
    /**
     * Fetch player information and statistics
     */
    fetchPlayerData(playerId: number, options?: DataFetcherOptions): Promise<{
        info: any;
        stats: any;
    }>;
    /**
     * Fetch recent match results for analysis
     */
    fetchRecentResults(options?: {
        teamIds?: number[];
        startDate?: Date;
        endDate?: Date;
        limit?: number;
    } & DataFetcherOptions): Promise<any[]>;
    /**
     * Fetch comprehensive data for a match analysis
     */
    fetchMatchAnalysisData(matchId: number, options?: DataFetcherOptions): Promise<{
        match: any;
        team1Data: {
            info: any;
            stats: any;
        };
        team2Data: {
            info: any;
            stats: any;
        };
        rankings: any[];
        recentResults: any[];
    }>;
    /**
     * Fetch player rankings
     */
    fetchPlayerRankings(options?: DataFetcherOptions): Promise<any[]>;
    /**
     * Batch fetch multiple teams data
     */
    fetchMultipleTeamsData(teamIds: number[], options?: DataFetcherOptions): Promise<Array<{
        teamId: number;
        info: any;
        stats: any;
    }>>;
    /**
     * Get client statistics
     */
    getClientStats(): {
        requests: number;
        lastRequestTime: number;
        cacheStats: any;
    };
}
//# sourceMappingURL=data-fetcher.d.ts.map