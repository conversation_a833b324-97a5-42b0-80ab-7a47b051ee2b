"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DateUtils = void 0;
const date_fns_1 = require("date-fns");
class DateUtils {
    /**
     * Get date based on mode
     */
    static getAnalysisDate(mode, customDate) {
        const now = new Date();
        switch (mode) {
            case 'today':
                return (0, date_fns_1.startOfDay)(now);
            case 'tomorrow':
                return (0, date_fns_1.startOfDay)((0, date_fns_1.addDays)(now, 1));
            case 'custom':
                if (!customDate) {
                    throw new Error('Custom date string required for custom mode');
                }
                const parsed = (0, date_fns_1.parseISO)(customDate);
                if (!(0, date_fns_1.isValid)(parsed)) {
                    throw new Error(`Invalid date format: ${customDate}. Use YYYY-MM-DD format.`);
                }
                return (0, date_fns_1.startOfDay)(parsed);
            default:
                throw new Error(`Invalid date mode: ${mode}`);
        }
    }
    /**
     * Get date range for analysis (start and end of day)
     */
    static getDateRange(date) {
        return {
            start: (0, date_fns_1.startOfDay)(date),
            end: (0, date_fns_1.endOfDay)(date),
        };
    }
    /**
     * Format date for HLTV API calls
     */
    static formatForHLTV(date) {
        return (0, date_fns_1.format)(date, 'yyyy-MM-dd');
    }
    /**
     * Format date for display
     */
    static formatForDisplay(date) {
        return (0, date_fns_1.format)(date, 'yyyy-MM-dd HH:mm');
    }
    /**
     * Format date for UTC display
     */
    static formatForUTC(date) {
        return (0, date_fns_1.format)(date, 'yyyy-MM-dd HH:mm') + ' UTC';
    }
    /**
     * Check if date is within last N days
     */
    static isWithinLastDays(date, days) {
        const now = new Date();
        const diffTime = now.getTime() - date.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays <= days;
    }
    /**
     * Check if date is within last N months
     */
    static isWithinLastMonths(date, months) {
        const now = new Date();
        const monthsAgo = new Date(now.getFullYear(), now.getMonth() - months, now.getDate());
        return date >= monthsAgo;
    }
    /**
     * Calculate days between two dates
     */
    static daysBetween(date1, date2) {
        const diffTime = Math.abs(date2.getTime() - date1.getTime());
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    /**
     * Get relative time string (e.g., "2 hours ago", "in 3 days")
     */
    static getRelativeTime(date) {
        const now = new Date();
        const diffMs = date.getTime() - now.getTime();
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        if (Math.abs(diffMinutes) < 60) {
            return diffMinutes > 0 ? `in ${diffMinutes} minutes` : `${Math.abs(diffMinutes)} minutes ago`;
        }
        else if (Math.abs(diffHours) < 24) {
            return diffHours > 0 ? `in ${diffHours} hours` : `${Math.abs(diffHours)} hours ago`;
        }
        else {
            return diffDays > 0 ? `in ${diffDays} days` : `${Math.abs(diffDays)} days ago`;
        }
    }
    /**
     * Check if it's a reasonable time for analysis (not too far in future)
     */
    static isReasonableAnalysisDate(date) {
        const now = new Date();
        const maxFutureDays = 30; // Don't analyze matches more than 30 days in future
        const diffDays = this.daysBetween(now, date);
        return date >= now && diffDays <= maxFutureDays;
    }
    /**
     * Parse various date string formats
     */
    static parseFlexibleDate(dateString) {
        // Try common formats
        const formats = [
            'yyyy-MM-dd',
            'MM/dd/yyyy',
            'dd/MM/yyyy',
            'yyyy-MM-dd HH:mm',
            'MM/dd/yyyy HH:mm',
        ];
        for (const formatStr of formats) {
            try {
                const parsed = (0, date_fns_1.parseISO)(dateString);
                if ((0, date_fns_1.isValid)(parsed)) {
                    return parsed;
                }
            }
            catch {
                // Continue to next format
            }
        }
        throw new Error(`Unable to parse date: ${dateString}`);
    }
}
exports.DateUtils = DateUtils;
//# sourceMappingURL=date-utils.js.map