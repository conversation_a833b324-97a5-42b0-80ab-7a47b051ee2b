// Jest setup file
import { config } from '@/config/settings';

// Set test environment
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error'; // Reduce logging noise in tests

// Mock console methods to reduce test output noise
const originalConsoleLog = console.log;
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

beforeAll(() => {
  // Suppress console output during tests unless explicitly needed
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  // Restore console methods
  console.log = originalConsoleLog;
  console.warn = originalConsoleWarn;
  console.error = originalConsoleError;
});

// Global test timeout
jest.setTimeout(30000);

// Mock external dependencies that might not be available in test environment
jest.mock('hltv', () => ({
  __esModule: true,
  default: {
    getMatches: jest.fn(),
    getMatch: jest.fn(),
    getTeamRanking: jest.fn(),
    getTeam: jest.fn(),
    getTeamStats: jest.fn(),
    getPlayer: jest.fn(),
    getPlayerStats: jest.fn(),
    getPlayerRanking: jest.fn(),
    getResults: jest.fn(),
    getEvents: jest.fn(),
    getEvent: jest.fn(),
    getMatchStats: jest.fn(),
  },
}));

export {};
