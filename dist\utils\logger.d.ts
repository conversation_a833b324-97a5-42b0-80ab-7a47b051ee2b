import winston from 'winston';
export declare const logger: winston.Logger;
export declare class Logger {
    /**
     * Log API request
     */
    static apiRequest(endpoint: string, params?: Record<string, unknown>): void;
    /**
     * Log API response
     */
    static apiResponse(endpoint: string, duration: number, success: boolean, error?: string): void;
    /**
     * Log analysis start
     */
    static analysisStart(date: string, mode: string): void;
    /**
     * Log analysis completion
     */
    static analysisComplete(date: string, duration: number, matchesAnalyzed: number, opportunitiesFound: number): void;
    /**
     * Log betting opportunity found
     */
    static opportunityFound(matchId: number, teams: string, betType: string, expectedValue: number, confidence: number): void;
    /**
     * Log data quality issues
     */
    static dataQualityIssue(type: string, description: string, severity: 'low' | 'medium' | 'high'): void;
    /**
     * Log rate limiting
     */
    static rateLimited(endpoint: string, retryAfter?: number): void;
    /**
     * Log cache operations
     */
    static cacheOperation(operation: 'hit' | 'miss' | 'set', key: string): void;
    /**
     * Log performance metrics
     */
    static performance(operation: string, duration: number, details?: Record<string, unknown>): void;
    /**
     * Log error with context
     */
    static error(message: string, error: Error, context?: Record<string, unknown>): void;
    /**
     * Log warning with context
     */
    static warn(message: string, context?: Record<string, unknown>): void;
    /**
     * Log info with context
     */
    static info(message: string, context?: Record<string, unknown>): void;
    /**
     * Log debug with context
     */
    static debug(message: string, context?: Record<string, unknown>): void;
}
export default logger;
//# sourceMappingURL=logger.d.ts.map